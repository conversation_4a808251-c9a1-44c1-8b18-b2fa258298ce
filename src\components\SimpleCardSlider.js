import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import SimpleCard from './SimpleCard';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

export default function SimpleCardSlider({ cardData = [] ,title = "Featured Topics", subtitle = "Explore our curated selection of important agricultural topics" }) {
  // Array of card data (static)
  console.log(cardData);
  // const cardData = [
  //   {
  //     id: 1,
  //     title: "Sustainable Farming Practices",
  //     description: "Learn about modern sustainable farming techniques that help preserve the environment while maximizing crop yields.",
  //     date: "May 15, 2023",
  //     image: "https://source.unsplash.com/random/300x200/?farming",
  //     category: "Farming",
  //     views: 245
  //   },
  //   {
  //     id: 2,
  //     title: "Organic Pest Control Methods",
  //     description: "Discover effective organic methods to control pests without harmful chemicals, keeping your crops healthy naturally.",
  //     date: "June 3, 2023",
  //     image: "https://source.unsplash.com/random/300x200/?garden",
  //     category: "Pest Control",
  //     views: 189
  //   },
  //   {
  //     id: 3,
  //     title: "Water Conservation in Agriculture",
  //     description: "Explore innovative water conservation techniques that can help farmers reduce water usage while maintaining productivity.",
  //     date: "July 12, 2023",
  //     image: "https://source.unsplash.com/random/300x200/?irrigation",
  //     category: "Water",
  //     views: 312
  //   },
  //   {
  //     id: 4,
  //     title: "Soil Health Management",
  //     description: "Learn how to maintain and improve soil health for better crop yields and sustainable farming practices.",
  //     date: "August 8, 2023",
  //     image: "https://source.unsplash.com/random/300x200/?soil",
  //     category: "Soil",
  //     views: 178
  //   },
  //   {
  //     id: 5,
  //     title: "Climate-Resilient Crop Varieties",
  //     description: "Discover crop varieties that are designed to withstand changing climate conditions and extreme weather events.",
  //     date: "September 21, 2023",
  //     image: "https://source.unsplash.com/random/300x200/?crops",
  //     category: "Crops",
  //     views: 203
  //   },
  //   {
  //     id: 6,
  //     title: "Precision Agriculture Technologies",
  //     description: "Explore how modern technology is revolutionizing farming with precision agriculture techniques and tools.",
  //     date: "October 5, 2023",
  //     image: "https://source.unsplash.com/random/300x200/?technology",
  //     category: "Technology",
  //     views: 287
  //   },
  //   {
  //     id: 7,
  //     title: "Crop Rotation Benefits",
  //     description: "Understand the importance of crop rotation in maintaining soil fertility and reducing pest problems. This ancient practice remains one of the most effective farming strategies.",
  //     date: "November 12, 2023",
  //     image: "https://source.unsplash.com/random/300x200/?crops",
  //     category: "Techniques",
  //     views: 156
  //   },
  //   {
  //     id: 8,
  //     title: "Organic Fertilizer Applications",
  //     description: "Learn about different types of organic fertilizers and how to apply them effectively for maximum plant nutrition. Compost, manure, and green manures all have specific benefits.",
  //     date: "December 3, 2023",
  //     image: "https://source.unsplash.com/random/300x200/?fertilizer",
  //     category: "Fertilizers",
  //     views: 198
  //   },
  //   {
  //     id: 9,
  //     title: "Greenhouse Farming Techniques",
  //     description: "Discover modern greenhouse farming methods that allow year-round production regardless of external climate conditions. Control every aspect of your growing environment.",
  //     date: "January 15, 2024",
  //     image: "https://source.unsplash.com/random/300x200/?greenhouse",
  //     category: "Greenhouse",
  //     views: 231
  //   },
  //   {
  //     id: 10,
  //     title: "Beekeeping for Crop Pollination",
  //     description: "Explore the benefits of integrating beekeeping with crop production to improve pollination and increase yields. Bees are essential partners in agricultural success.",
  //     date: "February 8, 2024",
  //     image: "https://source.unsplash.com/random/300x200/?bees",
  //     category: "Pollination",
  //     views: 267
  //   }
  // ];

  return (
    <div className="simple-card-slider-container">
      <div className="simple-card-slider-header">
        <h2>{title}</h2>
        <p>{subtitle}</p>
      </div>
      <div className="slider-container-inner">
        <Swiper
          modules={[Navigation, Pagination, Autoplay]}
          spaceBetween={20}
          slidesPerView={1}
          navigation
          pagination={{
            clickable: true,
            dynamicBullets: false,
            bulletActiveClass: 'swiper-pagination-bullet-active',
            bulletClass: 'swiper-pagination-bullet',
            horizontalClass: 'swiper-pagination-horizontal',
          }}
          autoplay={{
            delay: 3000,
            disableOnInteraction: false,
          }}
          breakpoints={{
            640: {
              slidesPerView: 1,
              spaceBetween: 20,
            },
            768: {
              slidesPerView: 2,
              spaceBetween: 30,
            },
            1024: {
              slidesPerView: 3,
              spaceBetween: 30,
            },
            1440: {
              slidesPerView: 4,
              spaceBetween: 30,
            },
          }}
          className="simple-card-swiper"
        >
        {/* Map through the card data to create slides */}
        {cardData.map((card) => (
          <SwiperSlide key={card.id}>
            <SimpleCard
              title={card.title}
              description={card.description}
              date={card.date}
              image={card.imageUrl}
              category={card.category}
              views={card.viewsCount}
            />
          </SwiperSlide>
        ))}
      </Swiper>
      </div>
    </div>
  );
}
