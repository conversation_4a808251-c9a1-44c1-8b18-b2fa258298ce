import ReadMoreBtn from './ReadMoreBtn';
import Icon from './Icon';
import logoImg from './imgs/whiteLeaf.png';
import { useEffect, useState, useRef } from 'react';

// Import images for the new showcase
import mainImage from './imgs/modern-farming-new.jpg';
import image1 from './imgs/4.jpg';
import image2 from './imgs/landingImg.jpg';
import image3 from './imgs/Powdery Mildew_ Treatment and Control on Plants.jpg';

export default function Landing() {
    const [isLoaded, setIsLoaded] = useState(false);
    const [activeImage, setActiveImage] = useState(0);

    // Image data for the showcase
    const showcaseImages = [
        {
            src: mainImage,
            alt: "Modern smart farming technology",
            title: "Smart Farming",
            description: "Innovative agricultural technologies for sustainable growth"
        },
        {
            src: image1,
            alt: "Plant disease diagnosis",
            title: "Plant Health",
            description: "Expert diagnosis and treatment for plant diseases"
        },
        {
            src: image2,
            alt: "Sustainable agriculture practices",
            title: "Sustainability",
            description: "Eco-friendly farming methods for a greener future"
        },
        {
            src: image3,
            alt: "Organic farming techniques",
            title: "Organic Methods",
            description: "Natural approaches to plant care and cultivation"
        }
    ];

    // Auto-rotate images
    useEffect(() => {
        const interval = setInterval(() => {
            setActiveImage((prev) => (prev + 1) % showcaseImages.length);
        }, 5000);

        return () => clearInterval(interval);
    }, [showcaseImages.length]);

    useEffect(() => {
        setIsLoaded(true);
    }, []);

    return (
        <div className='modern-hero'>
            <div className='hero-background'></div>
            <div className='hero-content'>
                <div className="hero-container">
                    <div className={`hero-text ${isLoaded ? 'loaded' : ''}`}>
                        <h1>Your Expert Guide to <span>Plant Health</span></h1>
                        <p>
                            Under the supervision of a group of agricultural experts, we provide you with the latest articles on plant diseases,
                            fertilizers, soil, and modern farming methods to ensure healthy and sustainable growth for your plants.
                        </p>
                        <div className="hero-cta">
                            <ReadMoreBtn>Explore Articles</ReadMoreBtn>
                        </div>
                    </div>

                    <div className={`hero-image-showcase ${isLoaded ? 'loaded' : ''}`}>
                        <div className="showcase-main">
                            {showcaseImages.map((image, index) => (
                                <div
                                    key={index}
                                    className={`showcase-item ${activeImage === index ? 'active' : ''}`}
                                    style={{zIndex: activeImage === index ? 2 : 1}}
                                >
                                    <img src={image.src} alt={image.alt} />
                                    <div className="showcase-overlay">
                                        <div className="showcase-badge">
                                            <div className="badge-icon">
                                                <img src={logoImg} alt="Logo" />
                                            </div>
                                            <div className="badge-content">
                                                <span className="badge-category">{image.title}</span>
                                                <span className="badge-description">{image.description}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div className="showcase-indicators">
                            {showcaseImages.map((_, index) => (
                                <button
                                    key={index}
                                    className={`indicator ${activeImage === index ? 'active' : ''}`}
                                    onClick={() => setActiveImage(index)}
                                    aria-label={`View image ${index + 1}`}
                                />
                            ))}
                        </div>

                        <div className="showcase-decoration">
                            <div className="decoration-circle circle-1"></div>
                            <div className="decoration-circle circle-2"></div>
                            <div className="decoration-line line-1"></div>
                            <div className="decoration-line line-2"></div>
                        </div>
                    </div>
                </div>

                <div className="hero-scroll">
                    <Icon/>
                </div>
            </div>
        </div>
    );
}