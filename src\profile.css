/* Profile Page Styles */

/* Fix for header transparency */
.profile-page-active .modern-header {
  background-color: rgba(20, 83, 45, 0.95);
  box-shadow: 0 4px 20px rgba(20, 83, 45, 0.2);
  z-index: 1000;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-page-active .modern-header .logo-text,
.profile-page-active .modern-header .nav-links li a,
.profile-page-active .modern-header .modern-search .search-button {
  color: var(--text-light);
}

.profile-page-active .modern-header .modern-search .search-button {
  background: transparent;
  border: none;
  padding: 10px 15px;
  transition: var(--transition);
}

.profile-page-active .modern-header .nav-links li a:hover {
  color: white;
}

.profile-page-active .modern-header .modern-search .search-input-wrapper {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 30px;
  overflow: hidden;
  transition: var(--transition);
}

.profile-page-active .modern-header .modern-search .search-input {
  width: 200px;
  padding: 10px 15px;
  border: none;
  background: transparent;
  color: var(--text-light);
  font-size: 14px;
  transition: var(--transition);
}

.profile-page-active .modern-header .modern-search .search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

/* Scrolled header on profile page */
.profile-page-active .modern-header.scrolled {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(20, 83, 45, 0.1);
  box-shadow: 0 4px 20px rgba(20, 83, 45, 0.1);
}

.profile-page-active .modern-header.scrolled .logo-text,
.profile-page-active .modern-header.scrolled .nav-links li a,
.profile-page-active .modern-header.scrolled .modern-search .search-button {
  color: var(--text-dark);
}

.profile-page-active .modern-header.scrolled .modern-search .search-input-wrapper {
  background-color: var(--background-alt);
}

.profile-page-active .modern-header.scrolled .modern-search .search-input {
  color: var(--text-dark);
}

.profile-page-active .modern-header.scrolled .modern-search .search-input::placeholder {
  color: rgba(44, 62, 80, 0.7);
}

.profile-page-active .modern-header .modern-search.focused .search-input-wrapper {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.profile-page-active .modern-header.scrolled .modern-search.focused .search-input-wrapper {
  box-shadow: 0 0 0 2px var(--primary);
  background-color: rgba(5, 150, 105, 0.05);
}

.profile-page-active .modern-header.scrolled .nav-links li a:hover {
  color: var(--primary);
}

/* Profile Page Layout */
.profile-page {
  padding-top: 80px;
  background-color: #f8fafc;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%2314532d' fill-opacity='0.03'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  min-height: 100vh;
  font-family: var(--font-primary);
  position: relative;
}

.profile-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(248, 250, 252, 0.8), rgba(248, 250, 252, 0.95));
  z-index: 0;
}

/* Profile Container */
.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 30px;
  display: flex;
  gap: 40px;
  position: relative;
  z-index: 1;
}

/* Profile Sidebar */
.profile-sidebar {
  width: 320px;
  flex-shrink: 0;
}

.profile-card {
  background-color: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  border: 1px solid rgba(229, 231, 235, 0.8);
  position: relative;
}

.profile-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 8px;
  background: linear-gradient(to right, #14532d, #059669);
  z-index: 1;
}

.profile-header {
  height: 120px;
  background: linear-gradient(to right, rgba(20, 83, 45, 0.9), rgba(20, 83, 45, 0.7));
  position: relative;
}

.profile-header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"%3E%3Cg fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Cpath opacity=".5" d="M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z"%2F%3E%3Cpath d="M6 5V0H5v5H0v1h5v94h1V6h94V5H6z"%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E');
  background-size: 100px 100px;
  opacity: 0.4;
  z-index: 0;
}

.profile-avatar-wrapper {
  position: absolute;
  bottom: -60px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 5px solid white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  background-color: white;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info {
  padding: 70px 25px 25px;
  text-align: center;
}

.profile-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 5px;
}

.profile-role {
  font-size: 0.9rem;
  color: #64748b;
  margin: 0 0 20px;
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f1f5f9;
}

.profile-stat {
  text-align: center;
}

.profile-stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #14532d;
  margin: 0;
}

.profile-stat-label {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0;
}

.profile-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.profile-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 12px;
  border-radius: 10px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.profile-action-primary {
  background-color: #14532d;
  color: white;
}

.profile-action-primary:hover {
  background-color: #059669;
  transform: translateY(-2px);
}

.profile-action-secondary {
  background-color: #f1f5f9;
  color: #1e293b;
}

.profile-action-secondary:hover {
  background-color: #e2e8f0;
  transform: translateY(-2px);
}

.profile-action-button i {
  font-size: 0.9rem;
}

/* Profile Content */
.profile-content {
  flex: 1;
}

.profile-tabs {
  background-color: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.profile-tabs-header {
  display: flex;
  border-bottom: 1px solid #f1f5f9;
}

.profile-tab {
  padding: 15px 25px;
  font-size: 0.95rem;
  font-weight: 600;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.profile-tab.active {
  color: #14532d;
}

.profile-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #14532d;
}

.profile-tab:hover:not(.active) {
  color: #1e293b;
  background-color: #f8fafc;
}

/* Posts Section */
.profile-posts {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.profile-post {
  background-color: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid rgba(229, 231, 235, 0.8);
  cursor: pointer;
}

.profile-post:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.profile-post-header {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f1f5f9;
}

.profile-post-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
}

.profile-post-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-post-info {
  flex: 1;
}

.profile-post-author {
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.profile-post-date {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0;
}

.profile-post-options {
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.3s ease;
}

.profile-post-options:hover {
  color: #1e293b;
}

.profile-post-content {
  display: flex;
}

.profile-post-text {
  flex: 1;
  padding: 20px;
}

.profile-post-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 10px;
}

.profile-post-excerpt {
  font-size: 0.95rem;
  color: #64748b;
  margin: 0;
  line-height: 1.6;
}

.profile-post-image {
  width: 200px;
  background-color: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.profile-post-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-top: 1px solid #f1f5f9;
}

.profile-post-stats {
  display: flex;
  align-items: center;
  gap: 15px;
}

.profile-post-stat {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.85rem;
  color: #64748b;
}

.profile-post-stat i {
  font-size: 0.9rem;
}

.profile-post-action {
  font-size: 0.85rem;
  font-weight: 600;
  color: #14532d;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
}

.profile-post-action:hover {
  color: #059669;
}

.profile-post-action i {
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.profile-post-action:hover i {
  transform: translateX(3px);
}

/* Empty State */
.profile-empty-state {
  background-color: white;
  border-radius: 15px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.profile-empty-state-icon {
  font-size: 3rem;
  color: #cbd5e1;
  margin-bottom: 20px;
}

.profile-empty-state-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 10px;
}

.profile-empty-state-text {
  font-size: 0.95rem;
  color: #64748b;
  margin: 0 0 20px;
}

.profile-empty-state-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #14532d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.profile-empty-state-button:hover {
  background-color: #059669;
  transform: translateY(-2px);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .profile-container {
    max-width: 95%;
    padding: 30px 20px;
  }
}

@media (max-width: 992px) {
  .profile-container {
    flex-direction: column;
  }
  
  .profile-sidebar {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }
  
  .profile-post-image {
    width: 150px;
  }
}

@media (max-width: 768px) {
  .profile-container {
    padding: 20px 15px;
  }
  
  .profile-tabs-header {
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
  }
  
  .profile-post-content {
    flex-direction: column;
  }
  
  .profile-post-image {
    width: 100%;
    height: 200px;
  }
  
  .profile-post-stats {
    flex-wrap: wrap;
  }
}

@media (max-width: 576px) {
  .profile-container {
    padding: 15px 10px;
  }
  
  .profile-tab {
    padding: 12px 15px;
    font-size: 0.85rem;
  }
  
  .profile-post-header,
  .profile-post-text,
  .profile-post-footer {
    padding: 12px 15px;
  }
  
  .profile-post-title {
    font-size: 1.1rem;
  }
  
  .profile-post-excerpt {
    font-size: 0.9rem;
  }
  
  .profile-empty-state {
    padding: 30px 15px;
  }
}
