import { useState } from 'react';

export default function SearchBar() {
    const [focused, setFocused] = useState(false);

    return (
        <div className={`modern-search ${focused ? 'focused' : ''}`}>
            <div className="search-input-wrapper">
                <input
                    className="search-input"
                    placeholder="Search..."
                    onFocus={() => setFocused(true)}
                    onBlur={() => setFocused(false)}
                />
                <button className="search-button">
                    <i className="fa-solid fa-magnifying-glass"></i>
                </button>
            </div>
        </div>
    );
}


// SearchBar
// <i className="fa-solid fa-magnifying-glass"></i>
