import React, { useState } from "react";
import Header from "./Header";
import Footer from "./Footer";
import "../contact.css";

export default function Contact() {
    const [formData, setFormData] = useState({
        name: "",
        email: "",
        subject: "",
        message: ""
    });

    const [formStatus, setFormStatus] = useState({
        submitted: false,
        success: false,
        message: ""
    });

    // Add a class to the body when the component mounts
    React.useEffect(() => {
        document.body.classList.add('contact-page-active');

        // Clean up function to remove the class when component unmounts
        return () => {
            document.body.classList.remove('contact-page-active');
        };
    }, []);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        // Simulate form submission
        setFormStatus({
            submitted: true,
            success: true,
            message: "Thank you for your message! We'll get back to you soon."
        });

        // Reset form after submission
        setTimeout(() => {
            setFormData({
                name: "",
                email: "",
                subject: "",
                message: ""
            });
            setFormStatus({
                submitted: false,
                success: false,
                message: ""
            });
        }, 5000);
    };

    return (
        <>
            <Header />
            <div className="modern-contact-page">
                <div className="contact-hero">
                    <div className="contact-hero-content">
                        <div className="contact-hero-text">
                            <h1>Get in Touch</h1>
                            <p>We'd love to hear from you. Let us know how we can help with your farming needs.</p>
                            <div className="contact-hero-stats">
                                <div className="hero-stat">
                                    <span className="hero-stat-number">10+</span>
                                    <span className="hero-stat-label">Years Experience</span>
                                </div>
                                <div className="hero-stat">
                                    <span className="hero-stat-number">500+</span>
                                    <span className="hero-stat-label">Happy Farmers</span>
                                </div>
                                <div className="hero-stat">
                                    <span className="hero-stat-number">50+</span>
                                    <span className="hero-stat-label">Expert Staff</span>
                                </div>
                            </div>
                        </div>
                        <div className="contact-hero-image">
                            <img src="https://images.unsplash.com/photo-1605000797499-95a51c5269ae?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80" alt="Farming" />
                            <div className="hero-image-shape"></div>
                        </div>
                    </div>
                </div>

                <div className="contact-container">
                    <div className="contact-info-section">
                        <div className="contact-info-card">
                            <div className="contact-info-icon">
                                <i className="fa-solid fa-location-dot"></i>
                            </div>
                            <h3>Our Location</h3>
                            <p>123 Green Valley Road<br />Cairo, Egypt</p>
                        </div>

                        <div className="contact-info-card">
                            <div className="contact-info-icon">
                                <i className="fa-solid fa-phone"></i>
                            </div>
                            <h3>Phone Number</h3>
                            <p>+20 ************<br />+20 ************</p>
                        </div>

                        <div className="contact-info-card">
                            <div className="contact-info-icon">
                                <i className="fa-solid fa-envelope"></i>
                            </div>
                            <h3>Email Address</h3>
                            <p><EMAIL><br /><EMAIL></p>
                        </div>

                        <div className="contact-info-card">
                            <div className="contact-info-icon">
                                <i className="fa-solid fa-clock"></i>
                            </div>
                            <h3>Working Hours</h3>
                            <p>Mon - Fri: 9:00 AM - 5:00 PM<br />Weekend: Closed</p>
                        </div>
                    </div>

                    <div className="contact-form-section">
                        <div className="contact-form-container">
                            <h2>Send Us a Message</h2>
                            {formStatus.submitted && (
                                <div className={`form-message ${formStatus.success ? 'success' : 'error'}`}>
                                    {formStatus.message}
                                </div>
                            )}
                            <form onSubmit={handleSubmit}>
                                <div className="form-group">
                                    <label htmlFor="name">
                                        <i className="fa-solid fa-user"></i> Your Name
                                    </label>
                                    <input
                                        type="text"
                                        id="name"
                                        name="name"
                                        value={formData.name}
                                        onChange={handleChange}
                                        placeholder="John Doe"
                                        required
                                    />
                                </div>

                                <div className="form-group">
                                    <label htmlFor="email">
                                        <i className="fa-solid fa-envelope"></i> Your Email
                                    </label>
                                    <input
                                        type="email"
                                        id="email"
                                        name="email"
                                        value={formData.email}
                                        onChange={handleChange}
                                        placeholder="<EMAIL>"
                                        required
                                    />
                                </div>

                                <div className="form-group">
                                    <label htmlFor="subject">
                                        <i className="fa-solid fa-tag"></i> Subject
                                    </label>
                                    <input
                                        type="text"
                                        id="subject"
                                        name="subject"
                                        value={formData.subject}
                                        onChange={handleChange}
                                        placeholder="How can we help you?"
                                        required
                                    />
                                </div>

                                <div className="form-group">
                                    <label htmlFor="message">
                                        <i className="fa-solid fa-message"></i> Your Message
                                    </label>
                                    <textarea
                                        id="message"
                                        name="message"
                                        value={formData.message}
                                        onChange={handleChange}
                                        placeholder="Tell us more about your inquiry..."
                                        rows="5"
                                        required
                                    ></textarea>
                                </div>

                                <button type="submit" className="submit-button">
                                    <i className="fa-solid fa-paper-plane"></i> Send Message
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div className="contact-map-section">
                    <div className="map-container">
                        <iframe
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d110502.76983794854!2d31.18401455!3d30.059482450000002!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14583fa60b21beeb%3A0x79dfb296e8423bba!2sCairo%2C%20Cairo%20Governorate!5e0!3m2!1sen!2seg!4v1652889148404!5m2!1sen!2seg"
                            width="100%"
                            height="450"
                            style={{border:0}}
                            allowFullScreen=""
                            loading="lazy"
                            referrerPolicy="no-referrer-when-downgrade"
                            title="Mazraaty Location"
                        ></iframe>
                    </div>
                </div>

                <div className="contact-social-section">
                    <div className="social-container">
                        <h2>Connect With Us</h2>
                        <p>Follow us on social media to stay updated with our latest news and events.</p>

                        <div className="social-icons">
                            <a href="https://facebook.com/mazraaty" className="social-icon facebook">
                                <i className="fa-brands fa-facebook-f"></i>
                            </a>
                            <a href="https://twitter.com/mazraaty" className="social-icon twitter">
                                <i className="fa-brands fa-x-twitter"></i>
                            </a>
                            <a href="https://instagram.com/mazraaty" className="social-icon instagram">
                                <i className="fa-brands fa-instagram"></i>
                            </a>
                            <a href="https://linkedin.com/company/mazraaty" className="social-icon linkedin">
                                <i className="fa-brands fa-linkedin-in"></i>
                            </a>
                            <a href="https://youtube.com/mazraaty" className="social-icon youtube">
                                <i className="fa-brands fa-youtube"></i>
                            </a>
                            <a href="https://t.me/mazraaty" className="social-icon telegram">
                                <i className="fa-brands fa-telegram"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div className="contact-partners-section">
                    <div className="partners-container">
                        <h2>Our Trusted Partners</h2>
                        <p>We collaborate with leading agricultural organizations to bring you the best farming solutions.</p>

                        <div className="partners-logos">
                            <div className="partner-logo">
                                <img src="https://via.placeholder.com/150x80?text=Partner+1" alt="Partner 1" />
                            </div>
                            <div className="partner-logo">
                                <img src="https://via.placeholder.com/150x80?text=Partner+2" alt="Partner 2" />
                            </div>
                            <div className="partner-logo">
                                <img src="https://via.placeholder.com/150x80?text=Partner+3" alt="Partner 3" />
                            </div>
                            <div className="partner-logo">
                                <img src="https://via.placeholder.com/150x80?text=Partner+4" alt="Partner 4" />
                            </div>
                            <div className="partner-logo">
                                <img src="https://via.placeholder.com/150x80?text=Partner+5" alt="Partner 5" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <Footer />
        </>
    );
}