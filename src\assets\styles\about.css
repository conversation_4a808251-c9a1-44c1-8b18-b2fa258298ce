/* About Page Styles */

/* Fix for header transparency */
.about-page-active .modern-header {
  background-color: rgba(20, 83, 45, 0.95);
  box-shadow: 0 4px 20px rgba(20, 83, 45, 0.2);
  z-index: 1000;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  animation: fadeInDown 0.5s ease-out;
}

.about-page-active .modern-header .logo-text,
.about-page-active .modern-header .nav-links li a,
.about-page-active .modern-header .search-button {
  color: var(--text-light);
}

.about-page-active .modern-header .nav-links li a:hover {
  color: white;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.about-page-active .modern-header .search-input-wrapper {
  background-color: rgba(255, 255, 255, 0.15);
}

.about-page-active .modern-header .search-input {
  color: var(--text-light);
}

.about-page-active .modern-header .search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

/* Scrolled header on about page */
.about-page-active .modern-header.scrolled {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(20, 83, 45, 0.1);
  box-shadow: 0 4px 20px rgba(20, 83, 45, 0.1);
}

.about-page-active .modern-header.scrolled .logo-text,
.about-page-active .modern-header.scrolled .nav-links li a,
.about-page-active .modern-header.scrolled .search-button {
  color: var(--text-dark);
}

.about-page-active .modern-header.scrolled .search-input-wrapper {
  background-color: var(--background-alt);
}

.about-page-active .modern-header.scrolled .search-input {
  color: var(--text-dark);
}

.about-page-active .modern-header.scrolled .search-input::placeholder {
  color: rgba(44, 62, 80, 0.7);
}

.about-page-active .modern-header.scrolled .nav-links li a:hover {
  color: var(--primary);
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* About Page General Styles */
.about-page {
  font-family: var(--font-primary);
  color: var(--text-dark);
  padding-top: 0; /* Ensure no extra padding at the top */
}

/* Decorative Separator */
.decorative-separator {
  position: absolute;
  top: 80px;
  left: 0;
  width: 100%;
  height: 160px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.separator-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.separator-icon-container {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.separator-icon-outer {
  position: absolute;
  width: 80px;
  height: 80px;
  border: 2px solid rgba(20, 83, 45, 0.3);
  border-radius: 50%;
  animation: rotate 10s linear infinite;
}

.separator-icon-outer::before,
.separator-icon-outer::after {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #14532d;
  border-radius: 50%;
}

.separator-icon-outer::before {
  top: -5px;
  left: calc(50% - 5px);
}

.separator-icon-outer::after {
  bottom: -5px;
  left: calc(50% - 5px);
}

.separator-icon {
  width: 60px;
  height: 60px;
  background-color: #14532d;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(20, 83, 45, 0.3);
  position: relative;
  z-index: 2;
  animation: pulse 2s infinite;
}

.separator-icon i {
  font-size: 24px;
  color: white;
}

.separator-line {
  width: 2px;
  height: 40px;
  background: linear-gradient(to bottom, #14532d, transparent);
  margin-top: -10px;
}

.separator-dots {
  display: flex;
  gap: 8px;
  margin-top: -20px;
}

.separator-dot {
  width: 6px;
  height: 6px;
  background-color: #14532d;
  border-radius: 50%;
  opacity: 0.7;
}

.separator-dot:nth-child(1) {
  animation: fadeInOut 1.5s infinite 0.2s;
}

.separator-dot:nth-child(2) {
  animation: fadeInOut 1.5s infinite 0.4s;
}

.separator-dot:nth-child(3) {
  animation: fadeInOut 1.5s infinite 0.6s;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(20, 83, 45, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(20, 83, 45, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(20, 83, 45, 0);
  }
}

@keyframes fadeInOut {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  margin-bottom: 40px;
}

.section-header h2 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-dark);
  margin-bottom: 15px;
  position: relative;
}

.section-header.centered {
  text-align: center;
}

.section-divider {
  height: 4px;
  width: 80px;
  background-color: #14532d;
  border-radius: 2px;
  margin-top: 15px;
}

.section-header.centered .section-divider {
  margin: 15px auto 0;
}

.highlight {
  color: #14532d;
}

/* Hero Section */
.about-hero {
  position: relative;
  height: 400px;
  background: linear-gradient(to right, rgba(20, 83, 45, 0.9), rgba(20, 83, 45, 0.7)), url(https://images.unsplash.com/photo-1625246333195-78d9c38ad449?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80);
  background-size: cover;
  background-position: center;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  margin-top: 200px; /* Increased space between header and hero section */
  margin-bottom: 60px;
  margin-left: 25px;
  margin-right: 25px;
}

.about-hero-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  width: 80%;
  max-width: 800px;
  z-index: 2;
}

.about-hero-content h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.about-hero-content p {
  font-size: 1.2rem;
  line-height: 1.6;
  margin: 0 auto;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

.about-hero-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 30%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.4), transparent);
}

/* Mission Section */
.about-mission {
  padding: 100px 0;
  background-color: white;
}

.about-mission .about-container {
  display: flex;
  align-items: center;
  gap: 60px;
}

.mission-content {
  flex: 1;
}

.mission-content p {
  font-size: var(--text-md);
  line-height: var(--leading-relaxed);
  color: #4b5563;
  margin-bottom: 20px;
}

.mission-image {
  flex: 1;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transform: rotate(2deg);
  transition: transform 0.5s ease;
}

.mission-image:hover {
  transform: rotate(0);
}

.mission-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Values Section */
.about-values {
  padding: 100px 0;
  background-color: #f8fafc;
  position: relative;
  overflow: hidden;
}

.about-values::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%2314532d" fill-opacity="0.03" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,250.7C960,235,1056,181,1152,165.3C1248,149,1344,171,1392,181.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-size: cover;
  background-position: center bottom;
  opacity: 0.8;
  z-index: 0;
}

.about-values .about-container {
  position: relative;
  z-index: 1;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 50px;
}

.value-card {
  background-color: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  text-align: center;
}

.value-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.value-icon {
  width: 70px;
  height: 70px;
  background-color: rgba(20, 83, 45, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.value-icon i {
  font-size: 28px;
  color: #14532d;
}

.value-card h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  margin-bottom: 15px;
  color: #1e293b;
}

.value-card p {
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  color: #64748b;
}

/* Story Section */
.about-story {
  padding: 100px 0;
  background-color: white;
}

.about-story .about-container {
  display: flex;
  gap: 60px;
}

.story-content {
  flex: 1;
}

.story-content p {
  font-size: var(--text-md);
  line-height: var(--leading-relaxed);
  color: #4b5563;
  margin-bottom: 20px;
}

.timeline {
  flex: 1;
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 2px;
  background-color: #14532d;
}

.timeline-item {
  position: relative;
  margin-bottom: 40px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -39px;
  top: 5px;
  width: 20px;
  height: 20px;
  background-color: #14532d;
  border-radius: 50%;
  border: 4px solid white;
  box-shadow: 0 0 0 2px #14532d;
}

.timeline-content {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.timeline-content h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: #14532d;
  margin-bottom: 10px;
}

.timeline-content p {
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  color: #4b5563;
  margin: 0;
}

/* Team Section */
.about-team {
  padding: 100px 0;
  background-color: #f8fafc;
}

.team-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 50px;
  font-size: var(--text-md);
  line-height: var(--leading-relaxed);
  color: #4b5563;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.team-member {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  text-align: center;
}

.team-member:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.member-image {
  height: 250px;
  overflow: hidden;
}

.member-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.team-member:hover .member-image img {
  transform: scale(1.05);
}

.team-member h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  margin: 20px 0 5px;
  color: #1e293b;
}

.member-role {
  font-size: var(--text-sm);
  color: #14532d;
  font-weight: var(--font-medium);
  margin-bottom: 10px;
}

.member-bio {
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  color: #64748b;
  padding: 0 20px 20px;
  margin: 0;
}

/* Join Us Section */
.about-join {
  padding: 100px 0;
  background: linear-gradient(rgba(20, 83, 45, 0.9), rgba(20, 83, 45, 0.9)),
              url('https://images.unsplash.com/photo-1523348837708-15d4a09cfac2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');
  background-size: cover;
  background-position: center;
  color: white;
  text-align: center;
}

.join-content {
  max-width: 800px;
  margin: 0 auto;
}

.join-content h2 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  margin-bottom: 20px;
}

.join-content p {
  font-size: var(--text-md);
  line-height: var(--leading-relaxed);
  margin-bottom: 40px;
}

.join-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.primary-button {
  display: inline-block;
  padding: 15px 30px;
  background-color: white;
  color: #14532d;
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.primary-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.secondary-button {
  display: inline-block;
  padding: 15px 30px;
  background-color: transparent;
  color: white;
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid white;
}

.secondary-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .about-mission .about-container,
  .about-story .about-container {
    flex-direction: column;
    gap: 40px;
  }

  .about-hero {
    height: 400px;
    margin-top: 140px; /* Adjust spacing for smaller screens */
    border-radius: 15px;
  }

  .decorative-separator {
    top: 70px;
  }

  .about-hero-content h1 {
    font-size: 2.5rem;
  }

  .about-hero-content p {
    font-size: 1.1rem;
  }

  .section-header h2 {
    font-size: calc(var(--text-3xl) * 0.9);
  }

  .mission-image {
    max-width: 600px;
    margin: 0 auto;
  }

  .timeline {
    padding-left: 20px;
  }
}

@media (max-width: 768px) {
  .about-mission,
  .about-values,
  .about-story,
  .about-team,
  .about-join {
    padding: 60px 0;
  }

  .about-hero {
    height: 350px;
    margin-top: 120px; /* Adjust spacing for smaller screens */
    border-radius: 15px;
  }

  .decorative-separator {
    top: 60px;
  }

  .separator-icon-container {
    width: 70px;
    height: 70px;
  }

  .separator-icon-outer {
    width: 70px;
    height: 70px;
  }

  .separator-icon {
    width: 50px;
    height: 50px;
  }

  .separator-icon i {
    font-size: 20px;
  }

  .separator-line {
    height: 30px;
  }

  .about-hero-content h1 {
    font-size: 1.8rem;
  }

  .about-hero-content p {
    font-size: 1rem;
  }

  .values-grid,
  .team-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .join-buttons {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .primary-button,
  .secondary-button {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 576px) {
  .about-mission,
  .about-values,
  .about-story,
  .about-team,
  .about-join {
    padding: 40px 0;
  }

  .about-hero {
    height: 300px;
    margin-top: 100px; /* Adjust spacing for mobile screens */
    border-radius: 12px;
    margin-bottom: 40px;
  }

  .decorative-separator {
    top: 50px;
    height: 60px;
  }

  .separator-icon-container {
    width: 60px;
    height: 60px;
  }

  .separator-icon-outer {
    width: 60px;
    height: 60px;
  }

  .separator-icon-outer::before,
  .separator-icon-outer::after {
    width: 8px;
    height: 8px;
  }

  .separator-icon-outer::before {
    top: -4px;
    left: calc(50% - 4px);
  }

  .separator-icon-outer::after {
    bottom: -4px;
    left: calc(50% - 4px);
  }

  .separator-icon {
    width: 45px;
    height: 45px;
  }

  .separator-icon i {
    font-size: 18px;
  }

  .separator-line {
    height: 25px;
  }

  .separator-dots {
    gap: 6px;
  }

  .separator-dot {
    width: 5px;
    height: 5px;
  }

  .about-hero-content h1 {
    font-size: 1.6rem;
  }

  .about-hero-content p {
    font-size: 0.9rem;
  }

  .section-header h2 {
    font-size: calc(var(--text-3xl) * 0.8);
  }

  .mission-content p,
  .story-content p,
  .team-intro,
  .join-content p {
    font-size: var(--text-base);
  }

  .values-grid,
  .team-grid {
    grid-template-columns: 1fr;
  }

  .timeline-dot {
    left: -30px;
  }
}
