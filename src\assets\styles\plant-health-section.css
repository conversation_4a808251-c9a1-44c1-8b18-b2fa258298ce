/* Plant Health Section Styles */
.plant-health-section {
  padding: 80px 0;
  background-color: #ffffff;
  position: relative;
  overflow: hidden;
}

.plant-health-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%2314532d" fill-opacity="0.03" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,250.7C960,235,1056,181,1152,165.3C1248,149,1344,171,1392,181.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-size: cover;
  background-position: center bottom;
  opacity: 0.8;
  z-index: 0;
}

.plant-health-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.plant-health-header {
  text-align: center;
  margin-bottom: 50px;
}

.plant-health-header h2 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-dark);
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

.plant-health-header h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: #059669;
  border-radius: 2px;
}

.plant-health-header h2 .highlight {
  color: #059669;
}

.plant-health-subtitle {
  font-size: var(--text-md);
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: var(--leading-relaxed);
  margin-top: 25px;
}

.plant-health-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 25px;
}

.plant-health-card {
  background-color: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.plant-health-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
  border-color: #059669;
}

.card-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: rgba(20, 183, 100, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.card-icon i {
  font-size: 20px;
  color: #059669;
}

.plant-health-card h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: #1e293b;
  margin-bottom: 12px;
}

.plant-health-card p {
  font-size: var(--text-sm);
  color: #64748b;
  line-height: var(--leading-relaxed);
  margin: 0;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .plant-health-cards {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
}

@media (max-width: 992px) {
  .plant-health-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .plant-health-section {
    padding: 60px 0;
  }

  .plant-health-header h2 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .plant-health-section {
    padding: 60px 0;
  }

  .plant-health-header h2 {
    font-size: 2rem;
  }

  .plant-health-subtitle {
    font-size: 1rem;
  }
}

@media (max-width: 576px) {
  .plant-health-section {
    padding: 40px 0;
  }

  .plant-health-cards {
    grid-template-columns: 1fr;
  }

  .plant-health-header h2 {
    font-size: 1.8rem;
  }

  .plant-health-subtitle {
    font-size: 1rem;
  }

  .plant-health-card {
    padding: 20px;
  }
}
