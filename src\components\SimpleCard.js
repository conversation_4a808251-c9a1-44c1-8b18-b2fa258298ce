import React from 'react';

export default function SimpleCard({
  title = "Sustainable Farming Practices",
  description = "Learn about modern sustainable farming techniques that help preserve the environment while maximizing crop yields.",
  date = "May 15, 2023",
  image = "https://source.unsplash.com/random/300x200/?agriculture"
}) {
  // Ensure description doesn't exceed a certain length
  const truncatedDescription = description.length > 150
    ? description.substring(0, 150) + '...'
    : description;
  return (
    <div className="simple-card">
      <div className="simple-card-image">
        <img src={image} alt={title} />
      </div>
      <div className="simple-card-content">
        <h3 className="simple-card-title">{title}</h3>
        <p className="simple-card-description">{truncatedDescription}</p>
        <div className="simple-card-footer">
          <span className="simple-card-date">{date}</span>
          <button className="simple-card-button">Read More</button>
        </div>
      </div>
    </div>
  );
}
