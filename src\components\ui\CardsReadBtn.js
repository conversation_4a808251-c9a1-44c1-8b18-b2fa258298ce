// import { <PERSON> } from "react-router";
import { useNavigate } from "react-router";

export default function CardsReadBtn({url}) {
    const navigate = useNavigate();

    const handleClick = () => {
        navigate(`${url}`);
    };
    return (
            <button className="card-read-more-btn" onClick={handleClick}>
                Read More
                <i className="fa-sharp fa-solid fa-circle-right"></i>
            </button>
    );
}























































// export default function CardsReadBtn({cardId, url}) {
//     const navigate = useNavigate();

//     const handleClick = () => {
//         // navigate(`/articles/categories/listing/${cardId}`);
//         navigate(`/articles/categories/listing/1`);
//     };
//     return (
//             <button className="card-read-more-btn" onClick={handleClick}>
//                 Read More
//                 <i className="fa-sharp fa-solid fa-circle-right"></i>
//             </button>
//     );
// }