/* Modern UI CSS */

:root {
  /* Base color: #059669 (<PERSON> Green) */
  --primary: #059669;
  --primary-dark: #047857;
  --secondary: #10b981;
  --secondary-dark: #065f46;
  --accent: #34d399;
  --text-dark: #1f2937;
  --text-light: #f9fafb;
  --background: #f8f9fa;
  --background-alt: #e9ecef;
  --border: #d1d5db;
  --shadow: rgba(5, 150, 105, 0.1);
  --radius: 8px;
  --transition: all 0.3s ease;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-primary);
  color: var(--text-dark);
  background-color: var(--background);
  line-height: var(--leading-normal);
  overflow-x: hidden; /* Prevent horizontal scrolling */
  width: 100%;
  position: relative;
}

a {
  text-decoration: none;
  color: inherit;
}

ul {
  list-style: none;
}

img {
  max-width: 100%;
  height: auto;
}

button {
  cursor: pointer;
  font-family: var(--font-primary);
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: var(--background-alt);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Container */
.hero-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@media (max-width: 992px) {
  .hero-container {
    flex-direction: column;
    padding: 0 15px;
  }
}

/* Header */
.modern-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 15px 0;
  transition: var(--transition);
  background-color: transparent;
}

.modern-header.scrolled {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
}

.modern-header.scrolled .nav-links a,
.modern-header.scrolled .logo-text {
  color: var(--text-dark);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 8px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-container {
  z-index: 1001;
}

.logo-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-wrapper img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-light);
  transition: var(--transition);
}

/* Desktop Navigation */
.desktop-nav {
  display: flex;
  align-items: center;
  gap: 30px;
}

.main-nav {
  margin-right: 20px;
}

.nav-links {
  display: flex;
  gap: 20px;
}

.nav-links li a {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-light);
  font-weight: 500;
  padding: 8px 12px;
  border-radius: var(--radius);
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1), letter-spacing 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: navItemFade 0.5s ease backwards;
  animation-delay: calc(0.1s * var(--nav-item-index, 0));
}

@keyframes navItemFade {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Previous hover effect - start */
/*
.nav-links li a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(5, 150, 105, 0);
  transition: background-color 0.3s ease;
  z-index: -1;
  border-radius: var(--radius);
}
*/
/* Previous hover effect - end */

/* Previous hover effect - continued */
/*
.nav-links li a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary), transparent);
  transition: width 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: translateX(-50%);
  opacity: 0;
}
*/

/*
.nav-links li a:hover {
  color: var(--primary);
  transform: translateY(-2px) scale(1.05);
  text-shadow: 0 0 1px rgba(5, 150, 105, 0.3);
  box-shadow: 0 4px 8px rgba(5, 150, 105, 0.1);
}

.nav-links li a:hover::before {
  background-color: rgba(5, 150, 105, 0.05);
}

.nav-links li a:hover::after {
  width: 80%;
  opacity: 1;
}
*/

/* Second hover effect - start */
/*
.nav-links li a::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.4s cubic-bezier(0.86, 0, 0.07, 1);
  z-index: 1;
}

.nav-links li a::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  border-radius: var(--radius);
  z-index: -1;
  transform: scale(0.9);
  transition: all 0.3s ease;
  opacity: 0;
}

.nav-links li a:hover {
  color: var(--primary);
  letter-spacing: 0.5px;
  text-shadow: 0 0 1px rgba(46, 204, 113, 0.2);
  animation: navHoverPulse 1.5s infinite;
}

@keyframes navHoverPulse {
  0%, 100% {
    text-shadow: 0 0 1px rgba(46, 204, 113, 0.2);
  }
  50% {
    text-shadow: 0 0 3px rgba(46, 204, 113, 0.4);
  }
}

.nav-links li a:hover::before {
  transform: scaleX(1);
  transform-origin: left;
}

.nav-links li a:hover::after {
  background-color: rgba(46, 204, 113, 0.08);
  transform: scale(1);
  opacity: 1;
}
*/

/* Third hover effect - start */
.nav-links li a {
  position: relative;
  overflow: visible;
}

.nav-links li a::before,
.nav-links li a::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.nav-links li a::before {
  top: -2px;
  left: -2px;
  border-top: 2px solid var(--primary);
  border-left: 2px solid var(--primary);
  transform: translate(-5px, -5px);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275), border-color 0.2s ease;
}

.nav-links li a::after {
  bottom: -2px;
  right: -2px;
  border-bottom: 2px solid var(--primary);
  border-right: 2px solid var(--primary);
  transform: translate(5px, 5px);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275), border-color 0.2s ease;
}

.nav-links li a:hover {
  color: var(--primary);
  transform: translateY(-1px);
  animation: borderPulse 1.5s infinite;
  text-shadow: 0 0 1px rgba(5, 150, 105, 0.3);
}

.nav-links li a:hover::before {
  opacity: 1;
  transform: translate(0, 0) rotate(-5deg);
}

.nav-links li a:hover::after {
  opacity: 1;
  transform: translate(0, 0) rotate(-5deg);
}

.nav-links li a span {
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.nav-links li a span::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(5, 150, 105, 0.05);
  transform: scaleY(0);
  transform-origin: bottom;
  transition: transform 0.3s ease;
  z-index: -1;
  border-radius: var(--radius);
}

.nav-links li a:hover span {
  font-weight: 600;
}

.nav-links li a:hover span::before {
  transform: scaleY(1);
}

@keyframes borderPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(5, 150, 105, 0);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(5, 150, 105, 0.1);
  }
}



.modern-header.scrolled .nav-links li a {
  color: var(--text-dark);
}

.modern-header.scrolled .nav-links li a:hover {
  color: var(--primary);
}

/*
.modern-header.scrolled .nav-links li a:hover {
  color: var(--primary);
  transform: translateY(-2px) scale(1.05);
  text-shadow: 0 0 1px rgba(5, 150, 105, 0.3);
  box-shadow: 0 4px 8px rgba(5, 150, 105, 0.1);
}

.modern-header.scrolled .nav-links li a:hover::before {
  background-color: rgba(5, 150, 105, 0.05);
}
*/

/* Second hover effect - scrolled state */
/*
.modern-header.scrolled .nav-links li a:hover {
  color: var(--primary);
}

.modern-header.scrolled .nav-links li a:hover::after {
  background-color: rgba(5, 150, 105, 0.1);
}
*/

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* Modern Search */
.modern-search {
  position: relative;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 30px;
  overflow: hidden;
  transition: var(--transition);
}

.modern-header.scrolled .search-input-wrapper {
  background-color: var(--background-alt);
}

.search-input {
  width: 200px;
  padding: 10px 15px;
  border: none;
  background: transparent;
  color: var(--text-light);
  font-size: 14px;
  transition: var(--transition);
}

.modern-header.scrolled .search-input {
  color: var(--text-dark);
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.modern-header.scrolled .search-input::placeholder {
  color: rgba(44, 62, 80, 0.7);
}

.search-input:focus {
  outline: none;
  width: 220px;
}

.search-button {
  background: transparent;
  border: none;
  padding: 10px 15px;
  color: var(--text-light);
  transition: var(--transition);
}

.modern-header.scrolled .search-button {
  color: var(--text-dark);
}

.modern-search.focused .search-input-wrapper {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.modern-header.scrolled .modern-search.focused .search-input-wrapper {
  box-shadow: 0 0 0 2px var(--primary);
  background-color: rgba(5, 150, 105, 0.05);
}

/* Write Button */
.write-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: var(--primary);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 30px;
  font-weight: 500;
  font-size: 14px;
  transition: var(--transition);
  box-shadow: 0 4px 10px rgba(5, 150, 105, 0.3);
}

.write-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(5, 150, 105, 0.4);
}

/* User Menu */
.user-profile {
  position: relative;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  border: 2px solid rgba(255, 255, 255, 0.7);
  transition: var(--transition);
}

.modern-header.scrolled .user-avatar {
  border-color: var(--primary);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background-color: var(--secondary);
  border-radius: 50%;
  border: 2px solid white;
}

/* Auth Buttons */
.auth-buttons {
  display: flex;
  gap: 10px;
}

.auth-btn {
  border: none;
  padding: 8px 16px;
  border-radius: 30px;
  font-weight: 500;
  font-size: 14px;
  transition: var(--transition);
}

.login-btn {
  background-color: transparent;
  color: var(--text-light);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.modern-header.scrolled .login-btn {
  color: var(--text-dark);
  border-color: var(--border);
}

.login-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.modern-header.scrolled .login-btn:hover {
  background-color: var(--background-alt);
}

.signup-btn {
  background-color: var(--primary);
  color: white;
  box-shadow: 0 4px 10px rgba(5, 150, 105, 0.3);
}

.signup-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(5, 150, 105, 0.4);
}

/* Dropdown Menu */
.drop-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 10px;
  background-color: white;
  border-radius: var(--radius);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  overflow: hidden;
  z-index: 100;
  animation: fadeIn 0.3s ease;
  transform-origin: top right;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.drop-menu li a {
  display: block;
  padding: 12px 20px;
  color: var(--text-dark);
  transition: var(--transition);
  font-size: 14px;
}

.drop-menu li a:hover {
  background-color: var(--background-alt);
  color: var(--primary);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 20px;
  background: transparent;
  border: none;
  cursor: pointer;
  z-index: 1001;
}

.mobile-menu-toggle span {
  display: block;
  width: 100%;
  height: 2px;
  background-color: var(--text-light);
  transition: var(--transition);
}

.modern-header.scrolled .mobile-menu-toggle span {
  background-color: var(--text-dark);
}

/* Mobile Navigation */
.mobile-nav {
  position: fixed;
  top: 0;
  right: -100%;
  width: 80%;
  max-width: 400px;
  height: 100vh;
  background-color: white;
  z-index: 1002;
  padding: 20px;
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: -5px 0 30px rgba(0, 0, 0, 0.1);
}

.mobile-nav.active {
  right: 0;
}

.mobile-nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.close-menu {
  background: transparent;
  border: none;
  font-size: 24px;
  color: var(--text-dark);
}

.mobile-search {
  margin-bottom: 30px;
}

.mobile-main-nav {
  margin-bottom: 30px;
}

.mobile-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mobile-links li a {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 15px;
  border-radius: var(--radius);
  color: var(--text-dark);
  font-weight: 500;
  transition: var(--transition);
}

.mobile-links li a:hover {
  background-color: var(--background-alt);
  color: var(--primary);
}

.mobile-links li a i {
  font-size: 18px;
  color: var(--primary);
}

.mobile-actions {
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mobile-user-info {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background-color: var(--background-alt);
  border-radius: var(--radius);
}

.mobile-user-info .user-avatar {
  width: 50px;
  height: 50px;
  border: none;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: var(--text-dark);
}

.user-details a {
  font-size: 14px;
  color: var(--primary);
}

.mobile-write {
  width: 100%;
  justify-content: center;
}

.mobile-logout {
  display: block;
  text-align: center;
  padding: 12px;
  color: #ef4444;
  font-weight: 500;
  border-top: 1px solid var(--border);
}

.mobile-auth-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mobile-auth-buttons .auth-btn {
  width: 100%;
  padding: 12px;
  text-align: center;
}

/* Media Queries for Header */
@media (max-width: 992px) {
  .desktop-nav {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .header-container {
    padding: 0 15px;
  }
}

/* Hero Section */
.modern-hero {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #059669, #10b981);
  z-index: -1;
}

.hero-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="0.05" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,250.7C960,235,1056,181,1152,165.3C1248,149,1344,171,1392,181.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-size: cover;
  background-position: center bottom;
  opacity: 0.8;
}

.hero-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at bottom left, rgba(5, 150, 105, 0.4), transparent 70%);
  z-index: 1;
}

.hero-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.hero-text {
  max-width: 550px;
  color: var(--text-light);
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.hero-text.loaded {
  opacity: 1;
  transform: translateY(0);
}

.hero-text h1 {
  font-size: 60px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 20px;
}

.hero-text h1 span {
  color: #6ee7b7;
}

.hero-text p {
  color: #ecfdf5e6;
  font-size: 18px;
  line-height: 1.7;
  margin-bottom: 30px;
  opacity: 0.9;
}

.hero-cta {
  margin-top: 30px;
}

/* Hero Image Showcase */
.hero-image-showcase {
  position: relative;
  opacity: 0;
  transform: translateX(30px);
  transition: opacity 0.8s ease 0.3s, transform 0.8s ease 0.3s;
  width: 550px;
}

.hero-image-showcase.loaded {
  opacity: 1;
  transform: translateX(0);
}

.showcase-main {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  z-index: 1;
}

.showcase-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transform: scale(1.05);
  transition: opacity 1s ease, transform 1.2s ease;
  z-index: 1;
}

.showcase-item.active {
  opacity: 1;
  transform: scale(1);
  z-index: 2;
}

.showcase-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 8s ease;
}

.showcase-item.active img {
  transform: scale(1.1);
}

.showcase-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(20, 83, 45, 0.85), rgba(5, 150, 105, 0.3));
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 30px;
  z-index: 2;
}

.showcase-badge {
  display: flex;
  align-items: center;
  gap: 15px;
  background-color: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  padding: 15px 20px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transform: translateY(-20px);
  opacity: 0;
  transition: transform 0.8s ease 0.3s, opacity 0.8s ease 0.3s;
  max-width: 80%;
}

.showcase-item.active .showcase-badge {
  transform: translateY(0);
  opacity: 1;
}

.badge-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.6);
  flex-shrink: 0;
}

.badge-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.badge-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.badge-category {
  color: white;
  font-weight: 700;
  font-size: 20px;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  letter-spacing: 0.5px;
}

.badge-description {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: 1.4;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Showcase Indicators */
.showcase-indicators {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
  position: relative;
  z-index: 3;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  outline: none;
}

.indicator.active {
  background-color: #14532d;
  border-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.2);
}

.indicator:hover {
  background-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

/* Decorative Elements */
.showcase-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(5, 150, 105, 0.3), rgba(16, 185, 129, 0.1));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 150px;
  height: 150px;
  top: -75px;
  right: -75px;
  animation: float-circle 8s ease-in-out infinite alternate;
}

.circle-2 {
  width: 100px;
  height: 100px;
  bottom: -50px;
  left: -50px;
  animation: float-circle 6s ease-in-out infinite alternate-reverse;
}

@keyframes float-circle {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-20px);
  }
}

.decoration-line {
  position: absolute;
  background: linear-gradient(90deg, rgba(5, 150, 105, 0.5), rgba(16, 185, 129, 0.1));
  height: 2px;
  border-radius: 2px;
}

.line-1 {
  width: 150px;
  top: 50px;
  right: -75px;
  transform: rotate(45deg);
  animation: pulse-line 4s ease-in-out infinite;
}

.line-2 {
  width: 100px;
  bottom: 70px;
  left: -50px;
  transform: rotate(-45deg);
  animation: pulse-line 4s ease-in-out 1s infinite;
}

@keyframes pulse-line {
  0%, 100% {
    opacity: 0.5;
    width: 100px;
  }
  50% {
    opacity: 0.8;
    width: 150px;
  }
}

.image-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
}

.circle-1 {
  width: 250px;
  height: 250px;
  background-color: rgba(0, 184, 148, 0.15);
  top: -120px;
  right: -70px;
  animation: float-circle 8s ease-in-out infinite;
}

@keyframes float-circle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}



.circle-2 {
  width: 180px;
  height: 180px;
  background-color: rgba(26, 188, 156, 0.15);
  bottom: -70px;
  left: -40px;
  animation: float-circle 6s ease-in-out infinite reverse;
}



@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

.decoration-line {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.15);
}

.line-1 {
  width: 250px;
  height: 3px;
  transform: rotate(45deg);
  top: 60px;
  right: -120px;
  background: linear-gradient(to right, transparent, rgba(26, 188, 156, 0.6), transparent);
  animation: pulse-line 4s ease-in-out infinite;
}

@keyframes pulse-line {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}



.line-2 {
  width: 200px;
  height: 3px;
  transform: rotate(-45deg);
  bottom: 80px;
  left: -70px;
  background: linear-gradient(to right, transparent, rgba(0, 184, 148, 0.6), transparent);
  animation: pulse-line 4s ease-in-out 1s infinite;
}





/* Scroll Indicator */
.hero-scroll {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
}

.scroll-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
  background-color: rgba(46, 204, 113, 0.2);
  padding: 10px 20px;
  border-radius: 30px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.scroll-indicator:hover {
  transform: translateY(-5px);
}

.scroll-text {
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.scroll-arrow {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Modern Button */
.modern-button {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 12px 24px;
  border: none;
  border-radius: 30px;
  font-weight: 500;
  font-size: 16px;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.modern-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.modern-button:hover::before {
  transform: translateX(100%);
}

.modern-button.primary {
  background-color: var(--primary);
  color: white;
  box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
}

.modern-button.primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
}

.modern-button.secondary {
  background-color: var(--secondary);
  color: white;
  box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
}

.modern-button.secondary:hover {
  background-color: var(--secondary-dark);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
}

.modern-button.outline {
  background-color: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
}

.modern-button.outline:hover {
  background-color: var(--primary);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.modern-button:hover .button-icon {
  transform: translateX(5px);
}

/* Media Queries for Hero */
@media (max-width: 1200px) {
  .hero-text h1 {
    font-size: 40px;
  }

  .image-card {
    width: 400px;
    height: 270px;
  }
}

/* Simple Card Styles */
.simple-card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  margin: 10px;
  height: 420px; /* Fixed height for all cards */
}

.simple-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.simple-card-image {
  width: 100%;
  height: 180px; /* Fixed height for all images */
  overflow: hidden;
  flex-shrink: 0; /* Prevent image from shrinking */
}

.simple-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.simple-card:hover .simple-card-image img {
  transform: scale(1.1);
}

.simple-card-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Distribute space evenly */
  height: 240px; /* Fixed height for content area */
  overflow: hidden; /* Prevent overflow */
}

.simple-card-title {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-dark);
}

.simple-card-description {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 5; /* Limit to 5 lines */
  line-clamp: 5; /* Standard property for compatibility */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.simple-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #eee;
  margin-top: auto; /* Push to bottom of container */
  flex-shrink: 0; /* Prevent footer from shrinking */
}

.simple-card-date {
  font-size: 12px;
  color: #888;
}

.simple-card-button {
  background-color: var(--primary);
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.simple-card-button:hover {
  background-color: var(--primary-dark);
}

/* Simple Card Slider Styles */
.simple-card-slider-container {
  width: 100vw;
  padding: 80px 0 80px 0;
  position: relative;
  background-color: var(--background);
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  overflow-x: hidden; /* Prevent horizontal scrolling */
  max-width: 100vw; /* Ensure it doesn't exceed viewport width */
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.simple-card-slider-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23059669" fill-opacity="0.05" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,250.7C960,235,1056,181,1152,165.3C1248,149,1344,171,1392,181.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-size: cover;
  background-position: center bottom;
  opacity: 0.5;
  z-index: 0;
}

.simple-card-slider-header {
  text-align: center;
  margin-bottom: 50px;
  padding: 0 20px;
}

.simple-card-slider-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

.simple-card-slider-header h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--primary);
  border-radius: 2px;
}

.simple-card-slider-header p {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

.slider-container-inner {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  overflow: visible; /* Allow pagination to be visible */
  width: 100%; /* Ensure it doesn't exceed container width */
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1; /* Ensure content is above the background */
}

.simple-card-swiper {
  padding: 20px 10px 80px 10px;
  overflow: visible; /* Changed to visible to show pagination */
  height: 520px; /* Adjusted height for the swiper container */
  width: 100%; /* Ensure it doesn't exceed container width */
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.simple-card-swiper .swiper-slide {
  height: auto; /* Let the slide adjust to the card height */
  display: flex;
}

.simple-card-slider-container .swiper-button-next,
.simple-card-slider-container .swiper-button-prev {
  color: white;
  background-color: var(--primary);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10; /* Ensure buttons are above content */
}

.simple-card-slider-container .swiper-button-next {
  right: 20px;
}

.simple-card-slider-container .swiper-button-prev {
  left: 20px;
}

.simple-card-slider-container .swiper-button-next:after,
.simple-card-slider-container .swiper-button-prev:after {
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.simple-card-slider-container .swiper-button-next:hover,
.simple-card-slider-container .swiper-button-prev:hover {
  background-color: var(--primary-dark);
  transform: scale(1.1);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.simple-card-slider-container .swiper-pagination {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 10;
  text-align: center;
  width: 100%;
  padding: 20px 0;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin-top: 20px !important;
}

.swiper-pagination-bullet {
  width: 30px !important;
  height: 8px !important;
  background-color: var(--primary) !important;
  opacity: 0.3 !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
  margin: 0 5px !important;
  display: inline-block !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
}

.swiper-pagination-bullet-active {
  opacity: 1 !important;
  background-color: var(--primary) !important;
  width: 45px !important;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2) !important;
}

@media (max-width: 992px) {
  .hero-container {
    flex-direction: column;
    justify-content: center;
    gap: 50px;
    padding-top: 50px;
  }

  .hero-text {
    text-align: center;
    max-width: 100%;
    padding: 0 20px;
  }

  .hero-cta {
    display: flex;
    justify-content: center;
  }

  .hero-image-showcase {
    width: 100%;
    max-width: 500px;
    margin: 0 auto 50px;
  }

  .showcase-main {
    height: 350px;
  }

  .showcase-badge {
    padding: 12px 15px;
  }

  .badge-icon {
    width: 50px;
    height: 50px;
  }

  .badge-category {
    font-size: 18px;
  }

  .badge-description {
    font-size: 13px;
  }

  .simple-card-slider-header h2 {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .hero-text h1 {
    font-size: 32px;
  }

  .hero-text p {
    font-size: 16px;
  }

  .hero-image-showcase {
    max-width: 100%;
    padding: 0 15px;
  }

  .showcase-main {
    height: 280px;
  }

  .showcase-overlay {
    padding: 20px;
  }

  .showcase-badge {
    padding: 10px;
    gap: 10px;
    max-width: 90%;
  }

  .badge-icon {
    width: 40px;
    height: 40px;
    padding: 8px;
  }

  .badge-category {
    font-size: 16px;
  }

  .badge-description {
    font-size: 12px;
    line-height: 1.3;
  }

  .indicator {
    width: 10px;
    height: 10px;
  }

  .modern-button {
    padding: 10px 20px;
    font-size: 14px;
  }

  .simple-card-slider-header h2 {
    font-size: 1.8rem;
  }

  .simple-card-slider-header p {
    font-size: 1rem;
  }
}
