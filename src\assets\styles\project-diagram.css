/* Project Diagram Section Styles */
.project-diagram-section {
  padding: 100px 0;
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;
}

.project-diagram-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
}

.project-diagram-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.project-diagram-header {
  text-align: center;
  margin-bottom: 50px;
}

.project-diagram-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 15px;
}

.project-diagram-header .highlight {
  color: #059669;
}

.project-diagram-subtitle {
  font-size: 1.1rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Tabs Navigation */
.diagram-tabs {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.diagram-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background-color: white;
  border: 2px solid #e0e0e0;
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 600;
  color: #555;
  cursor: pointer;
  transition: all 0.3s ease;
}

.diagram-tab i {
  font-size: 1.1rem;
}

.diagram-tab:hover {
  border-color: #059669;
  color: #059669;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.diagram-tab.active {
  background-color: #059669;
  color: white;
  border-color: #059669;
  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
}

/* Tab Content */
.diagram-content {
  position: relative;
  min-height: 500px;
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.diagram-panel {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.5s ease, visibility 0.5s ease;
  padding: 30px;
}

.diagram-panel.active {
  opacity: 1;
  visibility: visible;
  position: relative;
}

.diagram-panel-content {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
}

.diagram-text-content {
  flex: 1;
  min-width: 300px;
}

.diagram-text-content h3 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 20px;
  font-weight: 600;
}

.diagram-text-content p {
  color: #666;
  line-height: 1.7;
  margin-bottom: 20px;
}

.diagram-visual {
  flex: 1;
  min-width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* About Us Visual */
.about-visual {
  position: relative;
  width: 100%;
  height: 400px;
}

.about-circle {
  position: absolute;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: white;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.about-circle i {
  font-size: 1.8rem;
  margin-bottom: 8px;
  color: #059669;
}

.about-circle span {
  font-weight: 600;
  color: #333;
}

.main-circle {
  width: 150px;
  height: 150px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  background-color: #059669;
}

.main-circle i, .main-circle span {
  color: white;
}

.sub-circle {
  width: 100px;
  height: 100px;
  z-index: 1;
}

.sc-1 {
  top: 20%;
  left: 20%;
}

.sc-2 {
  top: 20%;
  right: 20%;
}

.sc-3 {
  bottom: 20%;
  left: 20%;
}

.sc-4 {
  bottom: 20%;
  right: 20%;
}

.connector {
  position: absolute;
  background-color: #e0e0e0;
  z-index: 0;
}

.c1, .c2, .c3, .c4 {
  width: 100px;
  height: 2px;
  top: 50%;
  left: 50%;
  transform-origin: left center;
}

.c1 {
  transform: rotate(-135deg) translateY(-50%);
}

.c2 {
  transform: rotate(-45deg) translateY(-50%);
}

.c3 {
  transform: rotate(135deg) translateY(-50%);
}

.c4 {
  transform: rotate(45deg) translateY(-50%);
}

/* Features List */
.feature-list {
  list-style: none;
  padding: 0;
}

.feature-list li {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.feature-list li:last-child {
  border-bottom: none;
}

.feature-list li i {
  color: #059669;
  font-size: 1.2rem;
  margin-top: 3px;
}

.feature-list li h4 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 5px;
  font-weight: 600;
}

.feature-list li p {
  margin-bottom: 0;
  color: #666;
}

/* Features Visual */
.features-visual {
  position: relative;
  width: 100%;
  height: 400px;
}

.feature-icon-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.feature-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.feature-icon i {
  font-size: 1.8rem;
  color: #059669;
}

.feature-icon-container span {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.fi-1 {
  top: 10%;
  left: 50%;
  transform: translateX(-50%);
}

.fi-2 {
  top: 40%;
  right: 10%;
}

.fi-3 {
  bottom: 10%;
  left: 50%;
  transform: translateX(-50%);
}

.fi-4 {
  top: 40%;
  left: 10%;
}

.fi-5 {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.feature-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.feature-center img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.feature-center-icon {
  width: 100px;
  height: 100px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 2px solid #059669;
}

.feature-connector {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 250px;
  height: 250px;
  border: 2px dashed #e0e0e0;
  border-radius: 50%;
  z-index: 0;
}

/* Benefits Grid */
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.benefit-card {
  background-color: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.benefit-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: #059669;
}

.benefit-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(5, 150, 105, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

.benefit-icon i {
  font-size: 1.5rem;
  color: #059669;
}

.benefit-card h4 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 10px;
  font-weight: 600;
}

.benefit-card p {
  color: #666;
  margin-bottom: 0;
  font-size: 0.95rem;
  line-height: 1.6;
}

/* How To Use Steps */
.steps-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.step {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #059669;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h4 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 5px;
  font-weight: 600;
}

.step-content p {
  color: #666;
  margin-bottom: 10px;
}

.step-action {
  margin-top: 10px;
}

.step-button {
  display: inline-block;
  padding: 8px 15px;
  background-color: rgba(5, 150, 105, 0.1);
  color: #059669;
  border-radius: 5px;
  font-weight: 600;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.step-button:hover {
  background-color: #059669;
  color: white;
}

/* Device Mockup */
.howto-visual {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.device-mockup {
  width: 280px;
  height: 500px;
  background-color: #333;
  border-radius: 30px;
  padding: 10px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  position: relative;
}

.device-screen {
  width: 100%;
  height: 100%;
  background-color: white;
  border-radius: 20px;
  overflow: hidden;
}

.screen-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.screen-header {
  height: 60px;
  background-color: #059669;
  display: flex;
  align-items: center;
  padding: 0 15px;
  gap: 10px;
}

.app-icon {
  width: 30px;
  height: 30px;
  background-color: white;
  border-radius: 8px;
}

.app-name {
  color: white;
  font-weight: 600;
}

.screen-body {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.screen-item {
  height: 100px;
  background-color: #f0f0f0;
  border-radius: 10px;
}

/* Call to Action */
.diagram-cta {
  text-align: center;
  margin-top: 50px;
  padding: 40px;
  background-color: #059669;
  border-radius: 20px;
  color: white;
}

.diagram-cta h3 {
  font-size: 1.8rem;
  margin-bottom: 25px;
  font-weight: 600;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 12px 25px;
  border-radius: 30px;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.cta-button.primary {
  background-color: white;
  color: #059669;
}

.cta-button.primary:hover {
  background-color: #f0f0f0;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.cta-button.secondary {
  background-color: transparent;
  border: 2px solid white;
  color: white;
}

.cta-button.secondary:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .project-diagram-section {
    padding: 80px 0;
  }

  .project-diagram-header h2 {
    font-size: 2.2rem;
  }

  .diagram-panel {
    padding: 20px;
  }

  .diagram-visual {
    margin-top: 30px;
  }

  .about-visual, .features-visual {
    height: 350px;
  }

  .main-circle {
    width: 120px;
    height: 120px;
  }

  .sub-circle {
    width: 80px;
    height: 80px;
  }
}

@media (max-width: 768px) {
  .project-diagram-section {
    padding: 60px 0;
  }

  .project-diagram-header h2 {
    font-size: 2rem;
  }

  .project-diagram-subtitle {
    font-size: 1rem;
  }

  .diagram-tab {
    padding: 10px 15px;
    font-size: 0.9rem;
  }

  .diagram-text-content h3 {
    font-size: 1.5rem;
  }

  .about-visual, .features-visual {
    height: 300px;
  }

  .main-circle {
    width: 100px;
    height: 100px;
  }

  .sub-circle {
    width: 70px;
    height: 70px;
  }

  .benefit-card {
    padding: 20px;
  }

  .diagram-cta h3 {
    font-size: 1.5rem;
  }

  .cta-button {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .project-diagram-section {
    padding: 50px 0;
  }

  .project-diagram-header h2 {
    font-size: 1.8rem;
  }

  .diagram-tabs {
    gap: 8px;
  }

  .diagram-tab {
    padding: 8px 12px;
    font-size: 0.8rem;
  }

  .diagram-panel {
    padding: 15px;
  }

  .about-visual, .features-visual {
    height: 250px;
  }

  .main-circle {
    width: 80px;
    height: 80px;
  }

  .main-circle i {
    font-size: 1.5rem;
  }

  .sub-circle {
    width: 60px;
    height: 60px;
  }

  .sub-circle i {
    font-size: 1.2rem;
  }

  .about-circle span {
    font-size: 0.8rem;
  }

  .feature-icon {
    width: 50px;
    height: 50px;
  }

  .feature-icon i {
    font-size: 1.3rem;
  }

  .feature-connector {
    width: 200px;
    height: 200px;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .step {
    gap: 15px;
  }

  .step-number {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .device-mockup {
    width: 220px;
    height: 400px;
  }

  .diagram-cta {
    padding: 30px 20px;
  }

  .diagram-cta h3 {
    font-size: 1.3rem;
  }

  .cta-buttons {
    gap: 15px;
  }

  .cta-button {
    width: 100%;
    justify-content: center;
  }
}
