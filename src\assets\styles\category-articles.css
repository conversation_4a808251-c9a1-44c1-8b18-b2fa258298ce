/* Category Articles Page Styles */

/* Fix for header transparency */
.category-articles-page-active .modern-header {
  background-color: rgba(20, 83, 45, 0.95);
  box-shadow: 0 4px 20px rgba(20, 83, 45, 0.2);
  z-index: 1000;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.category-articles-page-active .modern-header .logo-text,
.category-articles-page-active .modern-header .nav-links li a,
.category-articles-page-active .modern-header .modern-search .search-button {
  color: var(--text-light);
}

.category-articles-page-active .modern-header .modern-search .search-button {
  background: transparent;
  border: none;
  padding: 10px 15px;
  transition: var(--transition);
}

.category-articles-page-active .modern-header .nav-links li a:hover {
  color: white;
}

.category-articles-page-active .modern-header .modern-search .search-input-wrapper {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 30px;
  overflow: hidden;
  transition: var(--transition);
}

.category-articles-page-active .modern-header .modern-search .search-input {
  width: 200px;
  padding: 10px 15px;
  border: none;
  background: transparent;
  color: var(--text-light);
  font-size: 14px;
  transition: var(--transition);
}

.category-articles-page-active .modern-header .modern-search .search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

/* Scrolled header on category articles page */
.category-articles-page-active .modern-header.scrolled {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(20, 83, 45, 0.1);
  box-shadow: 0 4px 20px rgba(20, 83, 45, 0.1);
}

.category-articles-page-active .modern-header.scrolled .logo-text,
.category-articles-page-active .modern-header.scrolled .nav-links li a,
.category-articles-page-active .modern-header.scrolled .modern-search .search-button {
  color: var(--text-dark);
}

.category-articles-page-active .modern-header.scrolled .modern-search .search-input-wrapper {
  background-color: var(--background-alt);
}

.category-articles-page-active .modern-header.scrolled .modern-search .search-input {
  color: var(--text-dark);
}

.category-articles-page-active .modern-header.scrolled .modern-search .search-input::placeholder {
  color: rgba(44, 62, 80, 0.7);
}

.category-articles-page-active .modern-header .modern-search.focused .search-input-wrapper {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.category-articles-page-active .modern-header.scrolled .modern-search.focused .search-input-wrapper {
  box-shadow: 0 0 0 2px var(--primary);
  background-color: rgba(5, 150, 105, 0.05);
}

.category-articles-page-active .modern-header.scrolled .nav-links li a:hover {
  color: var(--primary);
}

/* Category Articles Page Layout */
.category-articles-page {
  padding-top: 80px;
  background-color: #f8fafc;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%2314532d' fill-opacity='0.03'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  min-height: 100vh;
  font-family: var(--font-primary);
  position: relative;
}

.category-articles-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(248, 250, 252, 0.8), rgba(248, 250, 252, 0.95));
  z-index: 0;
}

/* Breadcrumb Navigation */
.category-articles-breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  font-size: 0.9rem;
  position: relative;
  z-index: 1;
}

.category-articles-breadcrumb a {
  color: #64748b;
  text-decoration: none;
  transition: all 0.3s ease;
}

.category-articles-breadcrumb a:hover {
  color: #14532d;
}

.category-articles-breadcrumb .separator {
  margin: 0 10px;
  color: #94a3b8;
}

.category-articles-breadcrumb .current {
  color: #14532d;
  font-weight: 600;
}

/* Main Container */
.category-articles-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 30px;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 30px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 1;
  border: 1px solid rgba(229, 231, 235, 0.8);
  overflow: hidden;
  margin-top: 20px;
  margin-bottom: 40px;
  transition: all 0.3s ease;
}

.category-articles-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 8px;
  background: linear-gradient(to right, #14532d, #059669);
  border-radius: 30px 30px 0 0;
}

/* Category Header */
.category-articles-header {
  display: flex;
  background: linear-gradient(to right, rgba(20, 83, 45, 0.9), rgba(20, 83, 45, 0.7));
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
}

.category-articles-info {
  flex: 1;
  padding: 30px;
  color: white;
}

.category-articles-info h2 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.category-articles-info p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 20px;
  opacity: 0.9;
}

.category-articles-stats {
  display: flex;
  gap: 20px;
}

.category-articles-stat {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 8px 15px;
  border-radius: 30px;
  font-size: 0.9rem;
}

.category-articles-stat i {
  font-size: 1rem;
  opacity: 0.8;
}

/* Search and Filter Section */
.search-filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background-color: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.search-filter-section .search-input-wrapper {
  flex: 1;
  max-width: 500px;
  position: relative;
}

.search-filter-section .search-input-wrapper input {
  width: 100%;
  padding: 12px 20px;
  padding-left: 45px;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  font-size: 1rem;
  color: #1e293b;
  transition: all 0.3s ease;
}

.search-filter-section .search-input-wrapper input:focus {
  outline: none;
  border-color: #14532d;
  box-shadow: 0 0 0 3px rgba(20, 83, 45, 0.1);
}

.search-filter-section .search-input-wrapper i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  background-color: #f1f5f9;
  border: none;
  border-radius: 10px;
  font-size: 0.95rem;
  font-weight: 600;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-button:hover {
  background-color: #e2e8f0;
  color: #1e293b;
}

.filter-button i {
  font-size: 0.9rem;
}

/* Articles Grid */
.category-articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
}

/* Article Card */
.category-article-card {
  background-color: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.category-article-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.category-article-header {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.category-article-header img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s ease;
}

.category-article-card:hover .category-article-header img {
  transform: scale(1.1);
}

.category-article-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(20, 83, 45, 0.7), rgba(20, 83, 45, 0.9));
  opacity: 0.8;
  transition: all 0.3s ease;
}

.category-article-title {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20px;
  color: white;
  z-index: 1;
}

.category-article-title h3 {
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.category-article-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.category-article-excerpt {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #64748b;
  margin: 0 0 20px;
  flex: 1;
}

.category-article-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #f1f5f9;
}

.category-article-views {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

.category-article-views i {
  color: #94a3b8;
}

.read-more-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #14532d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.read-more-button:hover {
  background-color: #059669;
  transform: translateY(-2px);
}

.read-more-button i {
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.read-more-button:hover i {
  transform: translateX(3px);
}

/* Empty State */
.category-articles-empty {
  text-align: center;
  padding: 60px 20px;
  background-color: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.category-articles-empty-icon {
  font-size: 3rem;
  color: #cbd5e1;
  margin-bottom: 20px;
}

.category-articles-empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 10px;
}

.category-articles-empty-text {
  font-size: 1rem;
  color: #64748b;
  margin: 0 0 30px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.category-articles-empty-button {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 12px 24px;
  background-color: #14532d;
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-articles-empty-button:hover {
  background-color: #059669;
  transform: translateY(-2px);
}

/* Pagination */
.category-articles-pagination {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  margin: 0 5px;
  background-color: white;
  color: #64748b;
  font-weight: 600;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-button:hover {
  background-color: #f1f5f9;
  color: #1e293b;
}

.pagination-button.active {
  background-color: #14532d;
  color: white;
  border-color: #14532d;
}

.pagination-button.prev,
.pagination-button.next {
  width: auto;
  padding: 0 15px;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .category-articles-container {
    max-width: 95%;
    padding: 30px 25px;
  }
  
  .category-articles-info h2 {
    font-size: 2rem;
  }
}

@media (max-width: 992px) {
  .category-articles-header {
    flex-direction: column;
  }
  
  .category-articles-info {
    padding: 25px;
  }
  
  .category-articles-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .category-articles-container {
    padding: 25px 20px;
    border-radius: 20px;
  }
  
  .search-filter-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .search-filter-section .search-input-wrapper {
    width: 100%;
    max-width: 100%;
  }
  
  .filter-button {
    align-self: flex-end;
  }
  
  .category-articles-grid {
    grid-template-columns: 1fr;
  }
  
  .category-articles-stats {
    flex-wrap: wrap;
  }
}

@media (max-width: 576px) {
  .category-articles-container {
    padding: 20px 15px;
    border-radius: 15px;
  }
  
  .category-articles-info h2 {
    font-size: 1.5rem;
  }
  
  .category-article-header {
    height: 180px;
  }
  
  .category-article-title h3 {
    font-size: 1.2rem;
  }
  
  .pagination-button {
    width: 35px;
    height: 35px;
    margin: 0 3px;
  }
}
