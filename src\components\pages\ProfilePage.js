import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router';
import Header from '../shared/Header';
import Footer from '../shared/Footer';
import '../../assets/styles/profile.css';

import profileImg from '../../assets/images/team-03.png';

// with api

// export default function ProfilePage() {
//     const BASE_URL = process.env.REACT_APP_BASE_URL;

//     const [userData, setUserData] = useState({})
//     const [userPosts, setUserPosts] = useState([]);
//     const token = localStorage.getItem("token")


//     useEffect(() => {

//         fetch(`${BASE_URL}auth/me`, {
//             method: "GET",
//             headers: {
//               "Authorization": `Bearer ${token}`,
//               "Content-Type": "application/json",
//             },
//         })
//         .then(response => response.json())
//         .then(data => {
//             // localStorage.clear()
//             console.log("User Data:", data)
//             console.log(data.data.auth)
//             setUserData(data.data.auth);
//         })
//         .catch(error => console.error("Error:", error));

//         fetch(`${BASE_URL}user/articles`, {
//             method: "GET",
//             headers: {
//               "Authorization": `Bearer ${token}`,
//               "Content-Type": "application/json",
//             },
//         })
//         .then(response => response.json())
//         .then(data => {
//             // localStorage.clear()
//             console.log("Posts Data:", data)
//             console.log(data.data.articles)
//             setUserPosts(data.data.articles);
//         })
//         .catch(error => console.error("Error:", error));

//     }, [])

//     useEffect(() => {

//     }, [])
//     return(
//         <>
//             <Header background={true}/>
//             <div className='profile container' style={{minHeight: "150vh"}} >
//                 <div className='profile-content'>
//                     <h2 style={{textTransform:'capitalize'}}>{userData.name}</h2>
//                     <div className='profile-nav'>
//                         <span>Home</span>
//                         <span>About</span>
//                     </div>
//                     {
//                         // <ProfilePostContainer userData={userData}/>
//                         userPosts ? userPosts.map((post, index) => <ProfilePostContainer key={index} userPosts={post}/>) : <div style={{color:'#333', margin: '20px auto',textAlign: 'center'}}>No Posts For this User</div>
//                     }

//                     {/*
//                     <div className='posts-container'>
//                         <div className='posts-info'>

//                             <div className='user-data'>
//                                 <img src={userData.image_name} alt='img'/>
//                                 <div style={{textTransform:'capitalize'}}>{userData.name}</div>
//                             </div>

//                             <div className='list-name'>
//                                 Posts
//                             </div>

//                             <div className='options'>
//                                 <div className='posts-number'>No Stories <i className="fa-solid fa-lock"></i> </div>
//                                 <i className="fa-solid fa-ellipsis" style={{fontSize: '18px'}}></i>
//                             </div>


//                         </div>
//                         <div className='posts-imgs'>

//                             <div className='img-container' style={{width:'100%'}}>
//                                 <img src='' alt='' style={{width: '100%'}}/>
//                             </div>

//                         </div>
//                     </div> */}
//                 </div>
//                 <div className='user-info'>
//                     <img src={userData.image_name} alt='img'/> {/* src={profileImg} */}
//                     <h2 style={{textTransform:'capitalize'}}>{userData.name}</h2>
//                     <div>Edit Information</div>
//                 </div>
//             </div>
//             <Footer />
//         </>
//     );
// }



























// without api

export default function ProfilePage() {
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState('posts');

    // Add a class to the body when the component mounts
    useEffect(() => {
        document.body.classList.add('profile-page-active');

        // Clean up function to remove the class when component unmounts
        return () => {
            document.body.classList.remove('profile-page-active');
        };
    }, []);

    // Sample posts data
    const posts = [
        {
            id: 1,
            title: "Understanding Apple Tree Diseases: A Comprehensive Guide",
            excerpt: "Learn about the most common diseases affecting apple trees and how to identify and treat them effectively.",
            image: profileImg,
            author: "Mohamed Alaa Eldin",
            authorImage: profileImg,
            date: "May 15, 2023",
            views: 42,
            comments: 8,
            likes: 16
        },
        {
            id: 2,
            title: "Organic Solutions for Pest Control in Fruit Orchards",
            excerpt: "Discover eco-friendly methods to manage pests in your orchard without relying on harmful chemicals.",
            image: profileImg,
            author: "Mohamed Alaa Eldin",
            authorImage: profileImg,
            date: "April 22, 2023",
            views: 35,
            comments: 5,
            likes: 12
        },
        {
            id: 3,
            title: "Seasonal Care Guide for Citrus Trees",
            excerpt: "A month-by-month guide to caring for your citrus trees to ensure healthy growth and abundant harvests.",
            image: profileImg,
            author: "Mohamed Alaa Eldin",
            authorImage: profileImg,
            date: "March 10, 2023",
            views: 28,
            comments: 3,
            likes: 9
        }
    ];

    const handlePostClick = (id) => {
        console.log(`Clicked on post with id: ${id}`);
        navigate(`/articles/articleDetails/${id}`);
    };

    const handleTabChange = (tab) => {
        setActiveTab(tab);
    };

    return (
        <div className="profile-page">
            <Header background={true} />

            <div className="profile-container">
                {/* Profile Sidebar */}
                <div className="profile-sidebar">
                    <div className="profile-card">
                        <div className="profile-header">
                            <div className="profile-header-bg"></div>
                            <div className="profile-avatar-wrapper">
                                <div className="profile-avatar">
                                    <img src={profileImg} alt="Mohamed Alaa Eldin" />
                                </div>
                            </div>
                        </div>
                        <div className="profile-info">
                            <h2 className="profile-name">Mohamed Alaa Eldin</h2>
                            <p className="profile-role">Agricultural Expert</p>

                            <div className="profile-stats">
                                <div className="profile-stat">
                                    <p className="profile-stat-value">42</p>
                                    <p className="profile-stat-label">Posts</p>
                                </div>
                                <div className="profile-stat">
                                    <p className="profile-stat-value">1.2k</p>
                                    <p className="profile-stat-label">Followers</p>
                                </div>
                                <div className="profile-stat">
                                    <p className="profile-stat-value">156</p>
                                    <p className="profile-stat-label">Following</p>
                                </div>
                            </div>

                            <div className="profile-actions">
                                <button className="profile-action-button profile-action-primary">
                                    <i className="fas fa-edit"></i>
                                    <span>Edit Profile</span>
                                </button>
                                <button className="profile-action-button profile-action-secondary">
                                    <i className="fas fa-cog"></i>
                                    <span>Settings</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Profile Content */}
                <div className="profile-content">
                    <div className="profile-tabs">
                        <div className="profile-tabs-header">
                            <div
                                className={`profile-tab ${activeTab === 'posts' ? 'active' : ''}`}
                                onClick={() => handleTabChange('posts')}
                            >
                                <i className="fas fa-file-alt"></i> Posts
                            </div>
                            <div
                                className={`profile-tab ${activeTab === 'saved' ? 'active' : ''}`}
                                onClick={() => handleTabChange('saved')}
                            >
                                <i className="fas fa-bookmark"></i> Saved
                            </div>
                            <div
                                className={`profile-tab ${activeTab === 'drafts' ? 'active' : ''}`}
                                onClick={() => handleTabChange('drafts')}
                            >
                                <i className="fas fa-pencil-alt"></i> Drafts
                            </div>
                            <div
                                className={`profile-tab ${activeTab === 'about' ? 'active' : ''}`}
                                onClick={() => handleTabChange('about')}
                            >
                                <i className="fas fa-user"></i> About
                            </div>
                        </div>
                    </div>

                    {activeTab === 'posts' && (
                        <div className="profile-posts">
                            {posts.map((post) => (
                                <div
                                    key={post.id}
                                    className="profile-post"
                                    onClick={() => handlePostClick(post.id)}
                                >
                                    <div className="profile-post-header">
                                        <div className="profile-post-avatar">
                                            <img src={post.authorImage} alt={post.author} />
                                        </div>
                                        <div className="profile-post-info">
                                            <p className="profile-post-author">{post.author}</p>
                                            <p className="profile-post-date">{post.date}</p>
                                        </div>
                                        <div className="profile-post-options">
                                            <i className="fas fa-ellipsis-h"></i>
                                        </div>
                                    </div>
                                    <div className="profile-post-content">
                                        <div className="profile-post-text">
                                            <h3 className="profile-post-title">{post.title}</h3>
                                            <p className="profile-post-excerpt">{post.excerpt}</p>
                                        </div>
                                        <div className="profile-post-image">
                                            <img src={post.image} alt={post.title} />
                                        </div>
                                    </div>
                                    <div className="profile-post-footer">
                                        <div className="profile-post-stats">
                                            <div className="profile-post-stat">
                                                <i className="fas fa-eye"></i>
                                                <span>{post.views}</span>
                                            </div>
                                            <div className="profile-post-stat">
                                                <i className="fas fa-comment"></i>
                                                <span>{post.comments}</span>
                                            </div>
                                            <div className="profile-post-stat">
                                                <i className="fas fa-heart"></i>
                                                <span>{post.likes}</span>
                                            </div>
                                        </div>
                                        <div className="profile-post-action">
                                            <span>Read More</span>
                                            <i className="fas fa-arrow-right"></i>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}

                    {activeTab === 'saved' && (
                        <div className="profile-empty-state">
                            <div className="profile-empty-state-icon">
                                <i className="fas fa-bookmark"></i>
                            </div>
                            <h3 className="profile-empty-state-title">No saved articles yet</h3>
                            <p className="profile-empty-state-text">
                                Articles you save will appear here for easy access.
                            </p>
                            <button className="profile-empty-state-button" onClick={() => navigate('/articles')}>
                                <span>Browse Articles</span>
                                <i className="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    )}

                    {activeTab === 'drafts' && (
                        <div className="profile-empty-state">
                            <div className="profile-empty-state-icon">
                                <i className="fas fa-pencil-alt"></i>
                            </div>
                            <h3 className="profile-empty-state-title">No draft articles</h3>
                            <p className="profile-empty-state-text">
                                Start writing and save drafts to continue later.
                            </p>
                            <button className="profile-empty-state-button" onClick={() => navigate('/create-post')}>
                                <span>Create New Post</span>
                                <i className="fas fa-plus"></i>
                            </button>
                        </div>
                    )}

                    {activeTab === 'about' && (
                        <div className="profile-empty-state">
                            <div className="profile-empty-state-icon">
                                <i className="fas fa-user"></i>
                            </div>
                            <h3 className="profile-empty-state-title">About Mohamed Alaa Eldin</h3>
                            <p className="profile-empty-state-text">
                                Agricultural expert with over 10 years of experience in plant pathology and sustainable farming practices.
                                Specializing in organic pest control and disease management for fruit trees.
                            </p>
                            <button className="profile-empty-state-button" onClick={() => handleTabChange('posts')}>
                                <span>View Posts</span>
                                <i className="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    )}
                </div>
            </div>

            {/* <Footer /> */}
        </div>
    );
}