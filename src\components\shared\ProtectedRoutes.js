import { Navigate } from "react-router";
import { useEffect, useState } from "react";

export default function ProtectedRoutes({ children }) {
    const [isAuthenticated, setIsAuthenticated] = useState(null);

    useEffect(() => {
        // Check for token in localStorage
        const token = localStorage.getItem("token");
        setIsAuthenticated(!!token);
    }, []);

    // Show loading state while checking authentication
    if (isAuthenticated === null) {
        return (
            <div className="auth-loading">
                <div className="auth-loading-spinner"></div>
                <p>Checking authentication...</p>
            </div>
        );
    }

    // If authenticated, render the protected component
    // If not authenticated, redirect to login
    return isAuthenticated ? children : <Navigate to="/login" replace />;
}
