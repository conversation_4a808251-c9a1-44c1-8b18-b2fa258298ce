import './App.css';
import './modern.css';
import './auth.css';
import './login-register.css';
import Home from './components/Home';
import Contact from './components/Contact';
import Articles from './components/Articles';
import ArticleDetails from './components/ArticleDetails';
import SubCategoriesPage from './components/SubCategoriesPage';
import CategoryArticlePage from './components/CategoryArticlePage';
import Login from './components/Login';
import Register from './components/Register'
import ProfilePage from './components/ProfilePage';
import ProtectedRoutes from './components/ProtectedRoutes';
import CreatePost from './components/CreatePost';
import About from './components/About';
import { Routes, Route } from 'react-router';
// import backImage from './components/imgs/landingImg.jpg';


function App() {
  return (
    <div className="App">


      <Routes>
        <Route index element={<Home />} />

        <Route path='/login' element={<Login/>}/>
        <Route path='/register' element={<Register/>}/>

        <Route
          path="/profile"
          element={
            <ProtectedRoutes>
              <ProfilePage />
            </ProtectedRoutes>
          }
        />

        {/* <Route path='/profile' element={<ProfilePage/>}/> */}

        <Route path = "contact" element={<Contact />} />

        <Route path = "articles/categories" element={<Articles />} />

        <Route path = "articles/categories/subCategories/:id" element={<SubCategoriesPage />} />

        <Route path = "articles/categories/listing/:id" element={<CategoryArticlePage />} />

        <Route path = "articles/articleDetails/:id" element={<ArticleDetails />} />

        <Route
          path="/newPost"
          element={
            <ProtectedRoutes>
              <CreatePost />
            </ProtectedRoutes>
          }
        />

        <Route path = "about" element={<About />}/>
      </Routes>


    </div>
  );
}

export default App;
