import './assets/styles/App.css';
import './assets/styles/modern.css';
import './assets/styles/auth.css';
import './assets/styles/login-register.css';
import Home from './components/pages/Home';
import Contact from './components/pages/Contact';
import Articles from './components/articles/Articles';
import ArticleDetails from './components/articles/ArticleDetails';
import SubCategoriesPage from './components/articles/SubCategoriesPage';
import CategoryArticlePage from './components/articles/CategoryArticlePage';
import Login from './components/auth/Login';
import Register from './components/auth/Register'
import ProfilePage from './components/pages/ProfilePage';
import ProtectedRoutes from './components/shared/ProtectedRoutes';
import CreatePost from './components/articles/CreatePost';
import About from './components/pages/About';
import { Routes, Route } from 'react-router';
// import backImage from './components/imgs/landingImg.jpg';


function App() {
  return (
    <div className="App">


      <Routes>
        <Route index element={<Home />} />

        <Route path='/login' element={<Login/>}/>
        <Route path='/register' element={<Register/>}/>

        <Route
          path="/profile"
          element={
            <ProtectedRoutes>
              <ProfilePage />
            </ProtectedRoutes>
          }
        />

        {/* <Route path='/profile' element={<ProfilePage/>}/> */}

        <Route path = "contact" element={<Contact />} />

        <Route path = "articles/categories" element={<Articles />} />

        <Route path = "articles/categories/subCategories/:id" element={<SubCategoriesPage />} />

        <Route path = "articles/categories/listing/:id" element={<CategoryArticlePage />} />

        <Route path = "articles/articleDetails/:id" element={<ArticleDetails />} />

        <Route
          path="/newPost"
          element={
            <ProtectedRoutes>
              <CreatePost />
            </ProtectedRoutes>
          }
        />

        <Route path = "about" element={<About />}/>
      </Routes>


    </div>
  );
}

export default App;
