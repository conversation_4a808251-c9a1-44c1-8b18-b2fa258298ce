import img from '../../assets/images/sub-cat2.jpg';
import CardsReadBtn from '../ui/CardsReadBtn';

export default function CategoryArticleCard ({article}) {
    return (

        <div className="cat-art-card">
            <div className="cat-art-card-head grad">
                <h3 className='title'>What is apple scab ?</h3>
                <img src={img} alt='img' />
            </div>
            <div className="cat-art-card-text">
                Apple scab is a potentially serious fungal disease of ornamental and fruit trees in the rose family.
                Trees that are most commonly and severely affected include crabapple, hawthorn, mountain-ash, apple and pear.
                In ornamental trees, leaf loss caused by apple scab can make trees unsightly and aesthetically unappealing.
                In fruit trees, leaf loss can reduce fruit yield.
                In addition, the presence of apple scab on fruits can make the fruit difficult, if not impossible, to market.
            </div>
            <div className="cat-art-card-foot">
                <CardsReadBtn url={`/articles/articleDetails/1`} />
                <span><i className="fa-solid fa-eye"></i> 20</span>
            </div>
        </div>

    );
}




// with api

// import img from './imgs/sub-cat2.jpg';
// import CardsReadBtn from './CardsReadBtn';
// import Description from './Description';


// export default function CategoryArticleCard ({article}) {
//     return (

//         <div className="cat-art-card" >
//             <div className="cat-art-card-head grad">
//                 <h3 className='title'>{article.title}</h3>
//                 <img src={article.imageUrl} alt='img' />
//             </div>
//             <div className="cat-art-card-text">

//                 <Description text={article.description} />

//             </div>
//             <div className="cat-art-card-foot">
//                 <CardsReadBtn url={`/articles/articleDetails/${article.id}`}/> {/* `/articles/articleDetails/${article.id}` */}
//                 <span><i className="fa-solid fa-eye"></i>   {article.viewsCount}</span>
//             </div>
//         </div>
//     );
// }