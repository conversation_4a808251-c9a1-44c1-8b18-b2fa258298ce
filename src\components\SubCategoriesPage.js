import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router";
import { useParams } from "react-router";
import Header from "./Header";
import Footer from "./Footer";
import '../subcategories.css';

// Import sample images
import appleImg from './imgs/rodion-kutsaiev-049M_crau5k-unsplash <EMAIL>';

export default function SubCategoriesPage() {

    const BASE_URL = process.env.REACT_APP_BASE_URL;
    // console.log(BASE_URL);

    
    const [subCategories, setSubCategories] = useState([]);
    const [SubCategoriesPageStatics , setSubCategoriesPageStatics] = useState({});
    const {id} = useParams();
    
    const navigate = useNavigate();

    // Add a class to the body when the component mounts
    useEffect(() => {
        document.body.classList.add('subcategories-page-active');

        // Clean up function to remove the class when component unmounts
        return () => {
            document.body.classList.remove('subcategories-page-active');
        };
    }, []);

    useEffect(() => {
        // fetch(`${BASE_URL}sub-categories/get-by-categorie-id?categorieId=1`) 
        fetch(`${BASE_URL}sub-categories/get-by-categorie-id?categorieId=${id}`, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'ngrok-skip-browser-warning':  '69420'
            }   
        })
       .then(res=>res.json())
       .then(json=> {
           console.log(json);
           setSubCategories(json.data.subCategories);
           setSubCategoriesPageStatics(json.data.statics)
       })
}, [])

    // Sample subcategories data
    // const subcategories = [
    //     {
    //         id: 1,
    //         title: "Apple Scab",
    //         description: "A fungal disease that causes dark spots on apple leaves and fruits, leading to fruit deformation and reduced quality.",
    //         image: "https://images.unsplash.com/photo-1571989569743-3aa1d7d9bcc7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
    //         views: 42
    //     },
    //     {
    //         id: 2,
    //         title: "Fire Blight",
    //         description: "A bacterial disease affecting apple trees, causing branches to appear as if they've been scorched by fire. Can kill entire trees if left untreated.",
    //         image: "https://images.unsplash.com/photo-1625246333195-78d9c38ad449?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
    //         views: 35
    //     },
    //     {
    //         id: 3,
    //         title: "Cedar Apple Rust",
    //         description: "A fungal disease that requires both apple trees and cedar trees to complete its life cycle. Causes bright orange spots on apple leaves and fruits.",
    //         image: "https://images.unsplash.com/photo-1592982537447-7440770cbfc9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80",
    //         views: 28
    //     },
    //     {
    //         id: 4,
    //         title: "Apple Maggot",
    //         description: "An insect pest that lays eggs in developing apples. The larvae tunnel through the fruit, causing damage and decay.",
    //         image: "https://images.unsplash.com/photo-1622383563227-04401ab4e5ea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80",
    //         views: 19
    //     },
    //     {
    //         id: 5,
    //         title: "Powdery Mildew",
    //         description: "A fungal disease that appears as a white, powdery coating on leaves, shoots, and sometimes fruit. Can reduce photosynthesis and fruit quality.",
    //         image: "https://images.unsplash.com/photo-1620857493479-d892e9c3a0b0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
    //         views: 31
    //     },
    //     {
    //         id: 6,
    //         title: "Bitter Pit",
    //         description: "A physiological disorder caused by calcium deficiency. Creates small, dark, bitter-tasting spots on the fruit flesh, typically near the calyx end.",
    //         image: "https://images.unsplash.com/photo-1471193945509-9ad0617afabf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
    //         views: 24
    //     }
    // ];

    const handleSubcategoryClick = (id) => {
        console.log(`Clicked on subcategory with id: ${id}`);
        navigate(`/articles/categories/listing/${id}`);
    };

    return (
        <div className="subcategories-page">
            <Header background={true} />

            <div className="subcategories-container">
                {/* Breadcrumb */}
                <div className="subcategories-breadcrumb">
                    <a href="/">Home</a>
                    <span className="separator">/</span>
                    <a href="/articles">Articles</a>
                    <span className="separator">/</span>
                    <span className="current">{SubCategoriesPageStatics.categorieName}</span>
                </div>

                {/* Category Header */}
                <div className="category-header">
                    <div className="category-info">
                        <h2>{SubCategoriesPageStatics.categorieName}</h2>
                        <ul className="category-stats">
                            <li><i className="fas fa-bug"></i> {SubCategoriesPageStatics.diseaseCount} Different Diseases</li>
                            <li><i className="fas fa-book-open"></i> More than {SubCategoriesPageStatics.articleCount} Articles</li>
                            <li><i className="fas fa-eye"></i> More than {SubCategoriesPageStatics.articleReadsCount} Article Views</li>
                        </ul>
                    </div>
                    <div className="category-image" style={{ backgroundImage: "url('https://images.unsplash.com/photo-1570913149827-d2ac84ab3f9a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80')" }}>
                        <div className="category-image-content">
                            <div className="category-badge">{SubCategoriesPageStatics.diseaseCount} diseases</div>
                            <div className="category-image-circle">
                                <img src={SubCategoriesPageStatics.categorieUrlImage} alt="Apple" />
                            </div>
                            <div className="category-title">{SubCategoriesPageStatics.categorieName}</div>
                        </div>
                    </div>
                </div>

                {/* Search Section */}
                <div className="search-section">
                    <h3>What disease do you want to read about?</h3>
                    <div className="search-input-wrapper">
                        <i className="fas fa-search"></i>
                        <input
                            type="text"
                            placeholder="Search for diseases..."
                            className="subcategory-search-input"
                        />
                    </div>
                </div>

                {/* Subcategories Grid */}
                <div className="subcategories-grid">
                    {subCategories.map((subcategory) => (
                        <div
                            key={subcategory.id}
                            className="subcategory-card"
                            onClick={() => handleSubcategoryClick(subcategory.id)}
                        >
                            <div className="subcategory-header">
                                <div className="subcategory-image">
                                    <img src={subcategory.imageUrl} alt={subcategory.name} />
                                </div>
                            </div>
                            <div className="subcategory-content">
                                <h3 className="subcategory-title">{subcategory.name}</h3>
                                <p className="subcategory-description">{subcategory.descreption}</p>
                            </div>
                            <div className="subcategory-footer">
                                <div className="subcategory-views">
                                    <i className="fas fa-eye"></i>
                                    <span>{subcategory.viewArticlesCount}</span>
                                </div>
                                <button className="read-more-button">
                                    <span>Read More</span>
                                    <i className="fas fa-arrow-right"></i>
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            <Footer />
        </div>
    );
}