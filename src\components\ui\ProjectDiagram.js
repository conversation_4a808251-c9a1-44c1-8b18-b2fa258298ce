import React, { useState } from 'react';
import '../../assets/styles/project-diagram.css';
// Import a simple icon instead of SVG to avoid potential issues
import { FaLeaf } from 'react-icons/fa';

const ProjectDiagram = () => {
  // State to track which tab is active
  const [activeTab, setActiveTab] = useState('about');

  // Function to handle tab changes
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  return (
    <section className="project-diagram-section">
      <div className="project-diagram-container">
        <div className="project-diagram-header">
          <h2>Discover <span className="highlight">PlantGuard</span></h2>
          <p className="project-diagram-subtitle">
            Your complete guide to understanding our platform and how it can help you achieve healthier plants and sustainable farming
          </p>
        </div>

        {/* Interactive Tabs Navigation */}
        <div className="diagram-tabs">
          <button
            className={`diagram-tab ${activeTab === 'about' ? 'active' : ''}`}
            onClick={() => handleTabChange('about')}
          >
            <i className="fas fa-info-circle"></i>
            <span>About Us</span>
          </button>
          <button
            className={`diagram-tab ${activeTab === 'features' ? 'active' : ''}`}
            onClick={() => handleTabChange('features')}
          >
            <i className="fas fa-star"></i>
            <span>Key Features</span>
          </button>
          <button
            className={`diagram-tab ${activeTab === 'benefits' ? 'active' : ''}`}
            onClick={() => handleTabChange('benefits')}
          >
            <i className="fas fa-award"></i>
            <span>Benefits</span>
          </button>
          <button
            className={`diagram-tab ${activeTab === 'howto' ? 'active' : ''}`}
            onClick={() => handleTabChange('howto')}
          >
            <i className="fas fa-question-circle"></i>
            <span>How To Use</span>
          </button>
        </div>

        {/* Tab Content */}
        <div className="diagram-content">
          {/* About Us Tab */}
          <div className={`diagram-panel ${activeTab === 'about' ? 'active' : ''}`}>
            <div className="diagram-panel-content">
              <div className="diagram-text-content">
                <h3>Who We Are</h3>
                <p>
                  PlantGuard is a comprehensive agricultural platform developed by a team of agricultural experts, plant pathologists, and technology specialists. We're dedicated to making expert plant health knowledge accessible to everyone, from commercial farmers to home gardeners.
                </p>
                <h3>Our Mission</h3>
                <p>
                  Our mission is to empower farmers and plant enthusiasts with the knowledge and tools they need to identify, treat, and prevent plant diseases, while promoting sustainable farming practices that benefit both people and the planet.
                </p>
                <h3>Our Expertise</h3>
                <p>
                  Our content is created and verified by agricultural specialists with decades of combined experience in plant pathology, soil science, irrigation, organic farming, and modern agricultural technologies.
                </p>
              </div>
              <div className="diagram-visual">
                <div className="about-visual">
                  <div className="about-circle main-circle">
                    <i className="fas fa-leaf"></i>
                    <span>PlantGuard</span>
                  </div>
                  <div className="about-circle sub-circle sc-1">
                    <i className="fas fa-users"></i>
                    <span>Experts</span>
                  </div>
                  <div className="about-circle sub-circle sc-2">
                    <i className="fas fa-seedling"></i>
                    <span>Farmers</span>
                  </div>
                  <div className="about-circle sub-circle sc-3">
                    <i className="fas fa-microscope"></i>
                    <span>Research</span>
                  </div>
                  <div className="about-circle sub-circle sc-4">
                    <i className="fas fa-globe"></i>
                    <span>Sustainability</span>
                  </div>
                  <div className="connector c1"></div>
                  <div className="connector c2"></div>
                  <div className="connector c3"></div>
                  <div className="connector c4"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Features Tab */}
          <div className={`diagram-panel ${activeTab === 'features' ? 'active' : ''}`}>
            <div className="diagram-panel-content">
              <div className="diagram-text-content">
                <h3>Comprehensive Platform Features</h3>
                <ul className="feature-list">
                  <li>
                    <i className="fas fa-check-circle"></i>
                    <div>
                      <h4>Disease Identification</h4>
                      <p>Advanced visual recognition system to identify common plant diseases from photos or symptom descriptions.</p>
                    </div>
                  </li>
                  <li>
                    <i className="fas fa-check-circle"></i>
                    <div>
                      <h4>Treatment Solutions</h4>
                      <p>Customized treatment recommendations based on plant type, disease, and farming conditions.</p>
                    </div>
                  </li>
                  <li>
                    <i className="fas fa-check-circle"></i>
                    <div>
                      <h4>Expert Knowledge Base</h4>
                      <p>Extensive library of articles, guides, and resources written by agricultural specialists.</p>
                    </div>
                  </li>
                  <li>
                    <i className="fas fa-check-circle"></i>
                    <div>
                      <h4>Community Support</h4>
                      <p>Connect with other farmers and experts to share experiences and get advice.</p>
                    </div>
                  </li>
                  <li>
                    <i className="fas fa-check-circle"></i>
                    <div>
                      <h4>Sustainable Farming Guides</h4>
                      <p>Resources for implementing eco-friendly farming practices that improve yields while protecting the environment.</p>
                    </div>
                  </li>
                </ul>
              </div>
              <div className="diagram-visual">
                <div className="features-visual">
                  <div className="feature-icon-container fi-1">
                    <div className="feature-icon">
                      <i className="fas fa-bug"></i>
                    </div>
                    <span>Disease ID</span>
                  </div>
                  <div className="feature-icon-container fi-2">
                    <div className="feature-icon">
                      <i className="fas fa-flask"></i>
                    </div>
                    <span>Treatments</span>
                  </div>
                  <div className="feature-icon-container fi-3">
                    <div className="feature-icon">
                      <i className="fas fa-book"></i>
                    </div>
                    <span>Knowledge</span>
                  </div>
                  <div className="feature-icon-container fi-4">
                    <div className="feature-icon">
                      <i className="fas fa-users"></i>
                    </div>
                    <span>Community</span>
                  </div>
                  <div className="feature-icon-container fi-5">
                    <div className="feature-icon">
                      <i className="fas fa-leaf"></i>
                    </div>
                    <span>Sustainability</span>
                  </div>
                  <div className="feature-center">
                    <div className="feature-center-icon">
                      <FaLeaf size={50} color="#059669" />
                    </div>
                  </div>
                  <div className="feature-connector"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Benefits Tab */}
          <div className={`diagram-panel ${activeTab === 'benefits' ? 'active' : ''}`}>
            <div className="diagram-panel-content">
              <div className="diagram-text-content">
                <h3>How PlantGuard Benefits You</h3>
                <div className="benefits-grid">
                  <div className="benefit-card">
                    <div className="benefit-icon">
                      <i className="fas fa-chart-line"></i>
                    </div>
                    <h4>Increased Crop Yields</h4>
                    <p>Early disease detection and effective treatments help maximize your harvest potential.</p>
                  </div>
                  <div className="benefit-card">
                    <div className="benefit-icon">
                      <i className="fas fa-coins"></i>
                    </div>
                    <h4>Cost Savings</h4>
                    <p>Prevent crop losses and reduce unnecessary chemical applications through targeted treatments.</p>
                  </div>
                  <div className="benefit-card">
                    <div className="benefit-icon">
                      <i className="fas fa-clock"></i>
                    </div>
                    <h4>Time Efficiency</h4>
                    <p>Quick diagnosis and immediate access to solutions saves valuable time during critical growing periods.</p>
                  </div>
                  <div className="benefit-card">
                    <div className="benefit-icon">
                      <i className="fas fa-graduation-cap"></i>
                    </div>
                    <h4>Continuous Learning</h4>
                    <p>Stay updated with the latest agricultural practices and technologies through our regularly updated resources.</p>
                  </div>
                  <div className="benefit-card">
                    <div className="benefit-icon">
                      <i className="fas fa-globe-africa"></i>
                    </div>
                    <h4>Environmental Protection</h4>
                    <p>Learn sustainable farming methods that preserve soil health and protect local ecosystems.</p>
                  </div>
                  <div className="benefit-card">
                    <div className="benefit-icon">
                      <i className="fas fa-certificate"></i>
                    </div>
                    <h4>Quality Improvement</h4>
                    <p>Healthier plants produce better quality crops that can command premium prices in the market.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* How To Use Tab */}
          <div className={`diagram-panel ${activeTab === 'howto' ? 'active' : ''}`}>
            <div className="diagram-panel-content">
              <div className="diagram-text-content">
                <h3>Getting Started with PlantGuard</h3>
                <div className="steps-container">
                  <div className="step">
                    <div className="step-number">1</div>
                    <div className="step-content">
                      <h4>Create Your Account</h4>
                      <p>Sign up for a free account to access all our basic features and personalized recommendations.</p>
                      <div className="step-action">
                        <a href="/register" className="step-button">Register Now</a>
                      </div>
                    </div>
                  </div>
                  <div className="step">
                    <div className="step-number">2</div>
                    <div className="step-content">
                      <h4>Explore Knowledge Base</h4>
                      <p>Browse our extensive library of articles categorized by plant types, diseases, and farming practices.</p>
                      <div className="step-action">
                        <a href="/articles/categories" className="step-button">View Articles</a>
                      </div>
                    </div>
                  </div>
                  <div className="step">
                    <div className="step-number">3</div>
                    <div className="step-content">
                      <h4>Identify Plant Issues</h4>
                      <p>Use our symptom search or upload photos of affected plants to get accurate disease identification.</p>
                      <div className="step-action">
                        <a href="/diagnosis" className="step-button">Start Diagnosis</a>
                      </div>
                    </div>
                  </div>
                  <div className="step">
                    <div className="step-number">4</div>
                    <div className="step-content">
                      <h4>Get Treatment Plans</h4>
                      <p>Receive customized treatment recommendations based on your specific plant problems.</p>
                      <div className="step-action">
                        <a href="/treatments" className="step-button">View Treatments</a>
                      </div>
                    </div>
                  </div>
                  <div className="step">
                    <div className="step-number">5</div>
                    <div className="step-content">
                      <h4>Join The Community</h4>
                      <p>Connect with other farmers and experts to share experiences and get additional advice.</p>
                      <div className="step-action">
                        <a href="/community" className="step-button">Join Community</a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="diagram-visual">
                <div className="howto-visual">
                  <div className="device-mockup">
                    <div className="device-screen">
                      <div className="screen-content">
                        <div className="screen-header">
                          <div className="app-icon"></div>
                          <div className="app-name">PlantGuard</div>
                        </div>
                        <div className="screen-body">
                          <div className="screen-item"></div>
                          <div className="screen-item"></div>
                          <div className="screen-item"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="diagram-cta">
          <h3>Ready to improve your plant health and farming practices?</h3>
          <div className="cta-buttons">
            <a href="/register" className="cta-button primary">
              Get Started Now
              <i className="fas fa-arrow-right"></i>
            </a>
            <a href="/about" className="cta-button secondary">
              Learn More About Us
              <i className="fas fa-info-circle"></i>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProjectDiagram;
