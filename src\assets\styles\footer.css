/* Modern Footer Styles */

.modern-footer {
  background: linear-gradient(to right, var(--primary-dark), var(--primary));
  color: var(--text-light);
  padding: 0;
  font-family: 'Inter', sans-serif;
  position: relative;
  overflow: hidden;
}

.modern-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="0.05" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,250.7C960,235,1056,181,1152,165.3C1248,149,1344,171,1392,181.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-size: cover;
  background-position: center top;
  opacity: 0.1;
  z-index: 0;
}

.footer-wave {
  width: 100%;
  height: 50px;
  fill: var(--primary);
  display: block;
  margin-bottom: -5px;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 20px 40px;
  position: relative;
  z-index: 1;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.footer-column {
  display: flex;
  flex-direction: column;
  animation: fadeInUp 0.8s ease-out;
  animation-fill-mode: both;
}

.footer-column:nth-child(1) {
  animation-delay: 0.1s;
}

.footer-column:nth-child(2) {
  animation-delay: 0.3s;
}

.footer-column:nth-child(3) {
  animation-delay: 0.5s;
}

.footer-column:nth-child(4) {
  animation-delay: 0.7s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.footer-logo-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  /* background-color: white; */
  padding: 5px;
  transition: all 0.3s ease;
}

.footer-logo:hover .footer-logo-img {
  transform: rotate(360deg);
}

.footer-logo-text {
  font-size: 24px;
  font-weight: 700;
  color: white;
}

.footer-description {
  margin-bottom: 25px;
  line-height: 1.6;
  opacity: 0.9;
  font-size: 0.95rem;
}

.footer-heading {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.footer-heading::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--accent);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.footer-column:hover .footer-heading::after {
  width: 60px;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer-link {
  color: var(--text-light);
  opacity: 0.8;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer-link:hover {
  opacity: 1;
  transform: translateX(5px);
  color: var(--accent);
}

.footer-link i {
  font-size: 14px;
}

.footer-contact-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.footer-contact-icon {
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.3s ease;
}

.footer-contact-item:hover .footer-contact-icon {
  background-color: var(--accent);
  transform: scale(1.1);
}

.footer-contact-text {
  font-size: 0.95rem;
  opacity: 0.9;
}

.footer-social-icons {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.footer-social-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  transition: all 0.3s ease;
}

.footer-social-icon:hover {
  transform: translateY(-5px);
}

.footer-social-icon.linkedin:hover {
  background-color: #0a66c2;
}

.footer-social-icon.twitter:hover {
  background-color: #1da1f2;
}

.footer-social-icon.facebook:hover {
  background-color: #1877f2;
}

.footer-social-icon.instagram:hover {
  background-color: #e1306c;
}

.footer-newsletter {
  margin-top: 20px;
}

.footer-newsletter p {
  margin-bottom: 15px;
  font-size: 0.95rem;
  opacity: 0.9;
}

.footer-newsletter-form {
  display: flex;
  gap: 10px;
}

.footer-newsletter-input {
  flex: 1;
  padding: 12px 15px;
  border: none;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
}

.footer-newsletter-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.footer-newsletter-input:focus {
  outline: none;
  background-color: rgba(255, 255, 255, 0.15);
}

.footer-newsletter-button {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  background-color: var(--accent);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-newsletter-button:hover {
  background-color: #2dd4bf;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(52, 211, 153, 0.4);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20px 0;
  margin-top: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.footer-copyright {
  font-size: 14px;
  opacity: 0.8;
}

.footer-bottom-links {
  display: flex;
  gap: 20px;
}

.footer-bottom-link {
  color: var(--text-light);
  opacity: 0.8;
  font-size: 14px;
  transition: all 0.3s ease;
}

.footer-bottom-link:hover {
  opacity: 1;
  color: var(--accent);
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }

  .footer-bottom-links {
    justify-content: center;
  }

  .footer-newsletter-form {
    flex-direction: column;
  }

  .footer-newsletter-button {
    width: 100%;
  }
}
