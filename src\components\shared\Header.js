
import Logo from "./Logo"
import Pages from "../ui/Pages";
import SearchBar from "./SearchBar";
import userImg from '../../assets/images/team-03.png';
import greenlogoImg from '../../assets/images/greenLeaf.png';
import whitelogoImg from '../../assets/images/whiteLeaf.png';
import { useState, useEffect, useRef } from "react";
import { useNavigate, Link } from "react-router";

export default function Header({background}) {
    // localStorage.clear();
    // localStorage.setItem("token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vYnJpbGxpYW50LWV4aGliaXRpb24tY29hbGl0aW9uLWlvd2EudHJ5Y2xvdWRmbGFyZS5jb20vYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDMxOTkyMTksImV4cCI6MTc0MzIwMjgxOSwibmJmIjoxNzQzMTk5MjE5LCJqdGkiOiJiS1E5eXdXakxWcWt0M3NCIiwic3ViIjoiMTAiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.g2hZ10rJubQD7j1q7rhqIW1sa3LpQo9jwYSD_PxCH6c")
    const BASE_URL = process.env.REACT_APP_BASE_URL;
    let token = localStorage.getItem("token");

    const [showMenu, setShowMenu] = useState(false);
    const [showMobileMenu, setShowMobileMenu] = useState(false);
    const [userData, setUserData] = useState({});
    const [scrolled, setScrolled] = useState(false);
    const menuRef = useRef(null);
    const mobileMenuRef = useRef(null);

    // Fetch current user data if token exists
    useEffect(() => {
        if (token) {
            fetch(`${BASE_URL}auth/me`, {
                method: "GET",
                headers: {
                    "Authorization": `Bearer ${token}`,
                    "Content-Type": "application/json",
                    'ngrok-skip-browser-warning':  '69420'
                },
            })
            .then(response => response.json())
            .then(data => {
                console.log("User Data:", data)
                // console.log(data.data.auth)
                setUserData(data.data.auth);
            })
            .catch(error => console.error("Error:", error));
        }
    }, [token, BASE_URL]);

    useEffect(() => {
        // Add scroll event listener
        const handleScroll = () => {
            if (window.scrollY > 20) {
                setScrolled(true);
            } else {
                setScrolled(false);
            }
        };

        window.addEventListener('scroll', handleScroll);

        // Close menu when clicking outside
        const handleClickOutside = (event) => {
            if (menuRef.current && !menuRef.current.contains(event.target)) {
                setShowMenu(false);
            }
            if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target)) {
                setShowMobileMenu(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);

        return () => {
            window.removeEventListener('scroll', handleScroll);
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const navigate = useNavigate();

    const handleAuthNavigateLogin = () => {
        navigate('/login')
    }

    const handleAuthNavigateRegister = () => {
        navigate('/register')
    }

    const handleCreatePostNavigate = () => {
        navigate('/newPost')
    }

    const handleMenuBtn = () => {
        setShowMenu(!showMenu);
    }

    const toggleMobileMenu = () => {
        setShowMobileMenu(!showMobileMenu);
    }

    return (
        <header className={`modern-header ${scrolled ? 'scrolled' : ''}`}>
            <div className='header-container'>
                {/* Logo */}
                <div className='logo-container'>
                    <div className='logo-wrapper'>
                        <img src={scrolled? greenlogoImg: whitelogoImg} alt='Mazraaty Logo'/>
                        <span className="logo-text">Mazraaty</span>
                    </div>
                </div>

                {/* Mobile Menu Button */}
                <button className="mobile-menu-toggle" onClick={toggleMobileMenu}>
                    <span></span>
                    <span></span>
                    <span></span>
                </button>

                {/* Desktop Navigation */}
                <div className="desktop-nav">
                    <nav className="main-nav">
                        <Pages />
                    </nav>

                    <div className="header-actions">
                        <SearchBar />

                        {token && (
                            <button
                                onClick={handleCreatePostNavigate}
                                className="write-button"
                            >
                                <i className="fa-regular fa-pen-to-square"></i>
                                <span>Write</span>
                            </button>
                        )}
                        <div className="user-menu" ref={menuRef}>
                            {token ? (
                                <div className="user-profile">
                                    <div className="user-avatar" onClick={handleMenuBtn}>
                                        <img
                                            src={userData.image_name || userImg}
                                            alt="User profile"
                                        />

                                        <span className="user-indicator"></span>
                                    </div>
                                </div>
                            ) : (
                                <div className="auth-buttons">
                                    <button
                                        className="auth-btn login-btn"
                                        onClick={handleAuthNavigateLogin}
                                    >
                                        Log In
                                    </button>
                                    <button
                                        className="auth-btn signup-btn"
                                        onClick={handleAuthNavigateRegister}
                                    >
                                        Sign Up
                                    </button>
                                </div>
                            )}

                            {showMenu && (
                                <ul className="drop-menu">
                                    {token ? (
                                        <>
                                            <li><Link to="/profile">Profile</Link></li>
                                            <li>
                                                <button
                                                    onClick={() => {
                                                        localStorage.removeItem("token");
                                                        window.location.href = "/";
                                                    }}
                                                    style={{
                                                        background: "none",
                                                        border: "none",
                                                        cursor: "pointer",
                                                        textAlign: "left",
                                                        width: "100%",
                                                        padding: "0",
                                                        color: "inherit",
                                                        fontSize: "inherit"
                                                    }}
                                                >
                                                    Log Out
                                                </button>
                                            </li>
                                        </>
                                    ) : (
                                        <>
                                            <li><Link to="/register">Sign Up</Link></li>
                                            <li><Link to="/login">Log In</Link></li>
                                        </>
                                    )}
                                </ul>
                            )}
                        </div>
                    </div>
                </div>

                {/* Mobile Navigation */}
                <div className={`mobile-nav ${showMobileMenu ? 'active' : ''}`} ref={mobileMenuRef}>
                    <div className="mobile-nav-header">
                        <div className='logo-wrapper'>
                            <img src={greenlogoImg} alt='Mazraaty Logo'/>
                            <span className="logo-text">Mazraaty</span>
                        </div>
                        <button className="close-menu" onClick={toggleMobileMenu}>
                            <i className="fa-solid fa-times"></i>
                        </button>
                    </div>

                    <div className="mobile-search">
                        <SearchBar />
                    </div>

                    <nav className="mobile-main-nav">
                        <Pages mobile={true} />
                    </nav>

                    <div className="mobile-actions">
                        {token ? (
                            <>
                                <div className="mobile-user-info">
                                    <div className="user-avatar">
                                        <img src={userData.image_name} alt="User profile" />
                                    </div>
                                    <div className="user-details">
                                        <span className="user-name">{userData.name || 'User'}</span>
                                        <Link to="/profile">View Profile</Link>
                                    </div>
                                </div>
                                <button
                                    onClick={handleCreatePostNavigate}
                                    className="write-button mobile-write"
                                >
                                    <i className="fa-regular fa-pen-to-square"></i>
                                    <span>Write an Article</span>
                                </button>
                                <button
                                    onClick={() => {
                                        // localStorage.removeItem("token");
                                        window.location.href = "/";
                                    }}
                                    className="mobile-logout"
                                    style={{
                                        background: "none",
                                        border: "none",
                                        cursor: "pointer",
                                        textAlign: "center",
                                        width: "100%",
                                        padding: "10px 0",
                                        color: "#ef4444",
                                        fontSize: "1rem",
                                        fontWeight: "500"
                                    }}
                                >
                                    Log Out
                                </button>
                            </>
                        ) : (
                            <div className="mobile-auth-buttons">
                                <button
                                    className="auth-btn login-btn"
                                    onClick={handleAuthNavigateLogin}
                                >
                                    Log In
                                </button>
                                <button
                                    className="auth-btn signup-btn"
                                    onClick={handleAuthNavigateRegister}
                                >
                                    Sign Up
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </header>
    );
}





