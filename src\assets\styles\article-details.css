/* Article Details Page Styles */

/* Fix for header transparency */
.article-details-page-active .modern-header {
  background-color: rgba(20, 83, 45, 0.95);
  box-shadow: 0 4px 20px rgba(20, 83, 45, 0.2);
  z-index: 1000;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.article-details-page-active .modern-header .logo-text,
.article-details-page-active .modern-header .nav-links li a,
.article-details-page-active .modern-header .modern-search .search-button {
  color: var(--text-light);
}

.article-details-page-active .modern-header .modern-search .search-button {
  background: transparent;
  border: none;
  padding: 10px 15px;
  transition: var(--transition);
}

.article-details-page-active .modern-header .nav-links li a:hover {
  color: white;
}

.article-details-page-active .modern-header .modern-search .search-input-wrapper {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 30px;
  overflow: hidden;
  transition: var(--transition);
}

.article-details-page-active .modern-header .modern-search .search-input {
  width: 200px;
  padding: 10px 15px;
  border: none;
  background: transparent;
  color: var(--text-light);
  font-size: 14px;
  transition: var(--transition);
}

.article-details-page-active .modern-header .modern-search .search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

/* Scrolled header on article details page */
.article-details-page-active .modern-header.scrolled {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(20, 83, 45, 0.1);
  box-shadow: 0 4px 20px rgba(20, 83, 45, 0.1);
}

.article-details-page-active .modern-header.scrolled .logo-text,
.article-details-page-active .modern-header.scrolled .nav-links li a,
.article-details-page-active .modern-header.scrolled .modern-search .search-button {
  color: var(--text-dark);
}

.article-details-page-active .modern-header.scrolled .modern-search .search-input-wrapper {
  background-color: var(--background-alt);
}

.article-details-page-active .modern-header.scrolled .modern-search .search-input {
  color: var(--text-dark);
}

.article-details-page-active .modern-header.scrolled .modern-search .search-input::placeholder {
  color: rgba(44, 62, 80, 0.7);
}

.article-details-page-active .modern-header .modern-search.focused .search-input-wrapper {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.article-details-page-active .modern-header.scrolled .modern-search.focused .search-input-wrapper {
  box-shadow: 0 0 0 2px var(--primary);
  background-color: rgba(5, 150, 105, 0.05);
}

.article-details-page-active .modern-header.scrolled .nav-links li a:hover {
  color: var(--primary);
}

/* Article Details Page Layout */
.article-details-page {
  padding-top: 80px;
  background-color: #f8fafc;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%2314532d' fill-opacity='0.03'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  min-height: 100vh;
  font-family: var(--font-primary);
  position: relative;
}

.article-details-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(248, 250, 252, 0.8), rgba(248, 250, 252, 0.95));
  z-index: 0;
}

/* Breadcrumb Navigation */
.article-details-breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  font-size: 0.9rem;
  position: relative;
  z-index: 1;
}

.article-details-breadcrumb a {
  color: #64748b;
  text-decoration: none;
  transition: all 0.3s ease;
}

.article-details-breadcrumb a:hover {
  color: #14532d;
}

.article-details-breadcrumb .separator {
  margin: 0 10px;
  color: #94a3b8;
}

.article-details-breadcrumb .current {
  color: #14532d;
  font-weight: 600;
}

/* Main Container */
.article-details-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 30px;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 30px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 1;
  border: 1px solid rgba(229, 231, 235, 0.8);
  overflow: hidden;
  margin-top: 20px;
  margin-bottom: 40px;
  transition: all 0.3s ease;
}

.article-details-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 8px;
  background: linear-gradient(to right, #14532d, #059669);
  border-radius: 30px 30px 0 0;
}

/* Article Header */
.article-header {
  display: flex;
  background: linear-gradient(to right, rgba(20, 83, 45, 0.9), rgba(20, 83, 45, 0.7));
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
}

.article-info {
  flex: 1;
  padding: 30px;
  color: white;
}

.article-info h1 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.article-info p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 20px;
  opacity: 0.9;
}

.article-stats {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 0.95rem;
}

.article-stats i {
  opacity: 0.8;
  margin-right: 5px;
}

.article-image {
  width: 40%;
  overflow: hidden;
  position: relative;
}

.article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s ease;
}

.article-header:hover .article-image img {
  transform: scale(1.05);
}

.article-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(20, 83, 45, 0.7), transparent);
}

/* Article Content */
.article-content {
  padding: 20px 0;
}

.article-section {
  margin-bottom: 30px;
  background-color: white;
  border-radius: 15px;
  padding: 25px 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.article-section h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #14532d;
  margin: 0 0 15px;
  position: relative;
  padding-bottom: 10px;
}

.article-section h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: #14532d;
  border-radius: 2px;
}

.article-section p {
  font-size: 1.05rem;
  line-height: 1.7;
  color: #334155;
  margin: 0 0 15px;
}

.article-section ul {
  padding-left: 20px;
  margin: 15px 0;
}

.article-section li {
  font-size: 1.05rem;
  line-height: 1.7;
  color: #334155;
  margin-bottom: 10px;
  position: relative;
}

.article-section ul.checkmark {
  list-style: none;
  padding-left: 5px;
}

.article-section ul.checkmark li {
  padding-left: 30px;
  position: relative;
}

.article-section ul.checkmark li::before {
  content: '\f00c';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  left: 0;
  top: 2px;
  color: #14532d;
}

.article-tip {
  background-color: rgba(20, 83, 45, 0.1);
  border-left: 4px solid #14532d;
  padding: 15px 20px;
  border-radius: 0 10px 10px 0;
  margin: 20px 0;
  font-size: 1rem;
  color: #334155;
  position: relative;
  padding-left: 50px;
}

.article-tip::before {
  content: '\f0eb';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #14532d;
  font-size: 1.2rem;
}

/* Article Engagement */
.article-engagement {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid #e2e8f0;
  margin-top: 30px;
}

.article-actions {
  display: flex;
  gap: 20px;
}

.article-action {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  background-color: white;
  border-radius: 10px;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.article-action:hover {
  background-color: #f1f5f9;
  transform: translateY(-2px);
}

.article-action.liked {
  color: #ef4444;
}

.article-action.liked i {
  color: #ef4444;
}

.article-share {
  display: flex;
  gap: 10px;
}

.share-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 1px solid #e2e8f0;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
}

.share-button:hover {
  transform: translateY(-2px);
}

.share-button.facebook:hover {
  background-color: #1877f2;
  color: white;
  border-color: #1877f2;
}

.share-button.twitter:hover {
  background-color: #1da1f2;
  color: white;
  border-color: #1da1f2;
}

.share-button.linkedin:hover {
  background-color: #0077b5;
  color: white;
  border-color: #0077b5;
}

/* Comments Section */
.comments-section {
  margin-top: 40px;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.comments-header h3 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.comments-count {
  background-color: #14532d;
  color: white;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.comment {
  display: flex;
  gap: 15px;
  padding: 20px;
  background-color: white;
  border-radius: 15px;
  margin-bottom: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.comment-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.comment-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.comment-content {
  flex: 1;
}

.comment-author {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 5px;
}

.comment-text {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #64748b;
  margin: 0;
}

.comment-footer {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-top: 10px;
  font-size: 0.85rem;
  color: #94a3b8;
}

.comment-date {
  display: flex;
  align-items: center;
  gap: 5px;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.comment-action {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.comment-action:hover {
  color: #14532d;
}

/* Add Comment Form */
.add-comment {
  background-color: white;
  border-radius: 15px;
  padding: 20px;
  margin-top: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.add-comment-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.add-comment-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
}

.add-comment-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.add-comment-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.comment-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.comment-input {
  position: relative;
}

.comment-input textarea {
  width: 100%;
  padding: 15px;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  font-size: 1rem;
  color: #1e293b;
  resize: none;
  min-height: 100px;
  transition: all 0.3s ease;
}

.comment-input textarea:focus {
  outline: none;
  border-color: #14532d;
  box-shadow: 0 0 0 3px rgba(20, 83, 45, 0.1);
}

.comment-input textarea::placeholder {
  color: #94a3b8;
}

.comment-submit {
  align-self: flex-end;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 20px;
  background-color: #14532d;
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.comment-submit:hover {
  background-color: #059669;
  transform: translateY(-2px);
}

.comment-submit i {
  font-size: 0.9rem;
}

/* Related Articles */
.related-articles {
  margin-top: 60px;
}

.related-articles-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.related-articles-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  position: relative;
  padding-bottom: 10px;
}

.related-articles-header h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: #14532d;
  border-radius: 2px;
}

.view-all-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #14532d;
  font-size: 0.95rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.view-all-link:hover {
  color: #059669;
}

.view-all-link i {
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.view-all-link:hover i {
  transform: translateX(3px);
}

.related-articles-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.related-article-card {
  background-color: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(229, 231, 235, 0.8);
  cursor: pointer;
}

.related-article-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.related-article-image {
  height: 180px;
  overflow: hidden;
  position: relative;
}

.related-article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s ease;
}

.related-article-card:hover .related-article-image img {
  transform: scale(1.1);
}

.related-article-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.related-article-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 10px;
  line-height: 1.4;
}

.related-article-excerpt {
  font-size: 0.9rem;
  line-height: 1.6;
  color: #64748b;
  margin: 0 0 15px;
  flex: 1;
}

.related-article-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #f1f5f9;
  margin-top: auto;
}

.related-article-date {
  font-size: 0.85rem;
  color: #94a3b8;
  display: flex;
  align-items: center;
  gap: 5px;
}

.related-article-views {
  font-size: 0.85rem;
  color: #94a3b8;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .article-details-container {
    max-width: 95%;
    padding: 30px 25px;
  }
  
  .article-info h1 {
    font-size: 2rem;
  }
  
  .related-articles-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 992px) {
  .article-header {
    flex-direction: column;
  }
  
  .article-info {
    padding: 25px;
  }
  
  .article-image {
    width: 100%;
    height: 300px;
  }
  
  .article-image::after {
    background: linear-gradient(to bottom, rgba(20, 83, 45, 0.7), transparent);
  }
}

@media (max-width: 768px) {
  .article-details-container {
    padding: 25px 20px;
    border-radius: 20px;
  }
  
  .article-section {
    padding: 20px;
  }
  
  .article-info h1 {
    font-size: 1.8rem;
  }
  
  .article-engagement {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .related-articles-grid {
    grid-template-columns: 1fr;
  }
  
  .comment {
    padding: 15px;
  }
}

@media (max-width: 576px) {
  .article-details-container {
    padding: 20px 15px;
    border-radius: 15px;
  }
  
  .article-info h1 {
    font-size: 1.5rem;
  }
  
  .article-section h2 {
    font-size: 1.3rem;
  }
  
  .article-section p,
  .article-section li {
    font-size: 1rem;
  }
  
  .article-image {
    height: 200px;
  }
  
  .article-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .article-share {
    width: 100%;
    justify-content: space-between;
  }
}
