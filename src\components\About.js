import React, { useEffect } from 'react';
import Header from './Header';
import Footer from './Footer';
import '../about.css';

const About = () => {
  // Add a class to the body when the component mounts
  useEffect(() => {
    document.body.classList.add('about-page-active');

    // Clean up function to remove the class when component unmounts
    return () => {
      document.body.classList.remove('about-page-active');
    };
  }, []);

  return (
    <div className="about-page">
      <Header background={true} />

      {/* Decorative Separator */}
      <div className="decorative-separator">
        <div className="separator-content">
          <div className="separator-icon-container">
            <div className="separator-icon-outer"></div>
            <div className="separator-icon">
              <i className="fas fa-leaf"></i>
            </div>
          </div>
          <div className="separator-line"></div>
        </div>
        <div className="separator-dots">
          <div className="separator-dot"></div>
          <div className="separator-dot"></div>
          <div className="separator-dot"></div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="about-hero">
        <div className="about-hero-content">
          <h1>About <span className="highlight">AgriConnect</span></h1>
          <p>Empowering farmers with knowledge, technology, and community</p>
        </div>
        <div className="about-hero-overlay"></div>
      </section>

      {/* Mission Section */}
      <section className="about-mission">
        <div className="about-container">
          <div className="mission-content">
            <div className="section-header">
              <h2>Our Mission</h2>
              <div className="section-divider"></div>
            </div>
            <p>
              At AgriConnect, we're dedicated to revolutionizing agriculture through knowledge sharing and
              technological innovation. Our mission is to create a global community where farmers, agricultural
              experts, and enthusiasts can connect, learn, and grow together.
            </p>
            <p>
              We believe that sustainable farming practices and accessible agricultural knowledge are key to
              addressing global food security challenges while preserving our planet for future generations.
            </p>
          </div>
          <div className="mission-image">
            <img src="https://images.unsplash.com/photo-1500937386664-56d1dfef3854?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="Farmers in a field" />
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="about-values">
        <div className="about-container">
          <div className="section-header centered">
            <h2>Our Core Values</h2>
            <div className="section-divider"></div>
          </div>
          <div className="values-grid">
            <div className="value-card">
              <div className="value-icon">
                <i className="fas fa-seedling"></i>
              </div>
              <h3>Sustainability</h3>
              <p>We promote farming practices that meet present needs without compromising future generations.</p>
            </div>
            <div className="value-card">
              <div className="value-icon">
                <i className="fas fa-users"></i>
              </div>
              <h3>Community</h3>
              <p>We foster a supportive network where knowledge and experiences are freely shared.</p>
            </div>
            <div className="value-card">
              <div className="value-icon">
                <i className="fas fa-lightbulb"></i>
              </div>
              <h3>Innovation</h3>
              <p>We embrace new technologies and methods that improve agricultural efficiency and sustainability.</p>
            </div>
            <div className="value-card">
              <div className="value-icon">
                <i className="fas fa-book-open"></i>
              </div>
              <h3>Education</h3>
              <p>We believe in making agricultural knowledge accessible to everyone, everywhere.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="about-story">
        <div className="about-container">
          <div className="story-content">
            <div className="section-header">
              <h2>Our Story</h2>
              <div className="section-divider"></div>
            </div>
            <p>
              AgriConnect was founded in 2020 by a group of agricultural experts and technology enthusiasts who
              recognized the need for a platform that bridges the gap between traditional farming knowledge and
              modern agricultural innovations.
            </p>
            <p>
              What started as a small blog has grown into a comprehensive platform serving thousands of farmers
              across the globe. Our journey has been guided by a simple principle: when farmers succeed, we all succeed.
            </p>
            <p>
              Today, we continue to expand our resources, forge new partnerships, and develop innovative solutions
              to address the evolving challenges in agriculture.
            </p>
          </div>
          <div className="timeline">
            <div className="timeline-item">
              <div className="timeline-dot"></div>
              <div className="timeline-content">
                <h3>2020</h3>
                <p>AgriConnect was founded with a mission to connect farmers with essential knowledge.</p>
              </div>
            </div>
            <div className="timeline-item">
              <div className="timeline-dot"></div>
              <div className="timeline-content">
                <h3>2021</h3>
                <p>Launched our first mobile app, making agricultural information accessible on-the-go.</p>
              </div>
            </div>
            <div className="timeline-item">
              <div className="timeline-dot"></div>
              <div className="timeline-content">
                <h3>2022</h3>
                <p>Expanded our community to over 10,000 farmers across 50 countries.</p>
              </div>
            </div>
            <div className="timeline-item">
              <div className="timeline-dot"></div>
              <div className="timeline-content">
                <h3>2023</h3>
                <p>Introduced AI-powered plant disease identification and personalized crop recommendations.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="about-team">
        <div className="about-container">
          <div className="section-header centered">
            <h2>Meet Our Team</h2>
            <div className="section-divider"></div>
          </div>
          <p className="team-intro">
            Our diverse team brings together expertise in agriculture, technology, education, and community building.
            We're united by our passion for sustainable farming and our commitment to supporting agricultural communities worldwide.
          </p>
          <div className="team-grid">
            <div className="team-member">
              <div className="member-image">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="John Doe" />
              </div>
              <h3>John Doe</h3>
              <p className="member-role">Founder & CEO</p>
              <p className="member-bio">Agricultural economist with 15 years of experience in sustainable farming practices.</p>
            </div>
            <div className="team-member">
              <div className="member-image">
                <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80" alt="Jane Smith" />
              </div>
              <h3>Jane Smith</h3>
              <p className="member-role">Chief Technology Officer</p>
              <p className="member-bio">Tech innovator specializing in agricultural technology and data analytics.</p>
            </div>
            <div className="team-member">
              <div className="member-image">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80" alt="Michael Johnson" />
              </div>
              <h3>Michael Johnson</h3>
              <p className="member-role">Head of Research</p>
              <p className="member-bio">PhD in Agricultural Sciences with a focus on crop resilience and disease management.</p>
            </div>
            <div className="team-member">
              <div className="member-image">
                <img src="https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80" alt="Sarah Williams" />
              </div>
              <h3>Sarah Williams</h3>
              <p className="member-role">Community Manager</p>
              <p className="member-bio">Passionate about building bridges between traditional farming communities and modern agricultural practices.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Join Us Section */}
      <section className="about-join">
        <div className="about-container">
          <div className="join-content">
            <h2>Join Our Growing Community</h2>
            <p>
              Whether you're a seasoned farmer, an agricultural student, or simply passionate about sustainable food systems,
              there's a place for you in our community. Together, we can transform agriculture for a better future.
            </p>
            <div className="join-buttons">
              <a href="/register" className="primary-button">Sign Up Today</a>
              <a href="/contact" className="secondary-button">Contact Us</a>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default About;
