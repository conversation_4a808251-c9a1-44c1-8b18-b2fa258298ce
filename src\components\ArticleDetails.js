import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router";
import Header from "./Header";
import Footer from "./Footer";
import Description from "./Description";
import "../article-details.css";

// Import sample images
import articleImg from './imgs/Powdery Mildew_ Treatment and Control on Plants.jpg';
import userImg from "./imgs/team-03.png";



export default function ArticleDetails() {


    const BASE_URL = process.env.REACT_APP_BASE_URL;
    console.log(BASE_URL)

    const navigate = useNavigate();
    const { id } = useParams();

    const [articleStates, setArticleStates] = useState({})
    const [article, setArticle] = useState([])


    const [liked, setLiked] = useState(false);
    const [commentText, setCommentText] = useState('');

    // Add a class to the body when the component mounts
    useEffect(() => {
        document.body.classList.add('article-details-page-active');

        // Clean up function to remove the class when component unmounts
        return () => {
            document.body.classList.remove('article-details-page-active');
        };
    }, []);



    useEffect(() => {
        fetch(`${BASE_URL}find?articleId=${id}`, {
            headers: {
                // 'Content-Type': 'application/json',
                // 'Accept': 'application/json',
                'ngrok-skip-browser-warning':  '69420'
            }   
        })
        .then((response) => response.json())
        .then((data) => {
            // console.log(data.data.article.descreptions[0])

            console.log(data)

            setArticleStates(data.data.article.statics)
             
            console.log(data.data.article.descreptions)
            setArticle(data.data.article.descreptions)
        })
    }, []);

    // Sample comments data
    const comments = [
        {
            id: 1,
            author: "Ahmed Ali",
            avatar: userImg,
            text: "This article was very helpful! I've been struggling with powdery mildew on my cucumber plants and the baking soda solution worked wonders. Thank you for sharing this valuable information.",
            date: "2 days ago",
            likes: 5
        },
        {
            id: 2,
            author: "Sarah Johnson",
            avatar: userImg,
            text: "I've tried the milk spray method mentioned in this article and it's surprisingly effective. Would recommend to any organic gardeners dealing with this issue.",
            date: "1 week ago",
            likes: 3
        },
        {
            id: 3,
            author: "Mohamed Hassan",
            avatar: userImg,
            text: "Great article! I'd also add that improving air circulation by pruning dense foliage can help prevent powdery mildew from developing in the first place.",
            date: "2 weeks ago",
            likes: 7
        }
    ];

    // Sample related articles
    const relatedArticles = [
        {
            id: 101,
            title: "Black Spot Disease: Identification and Treatment",
            excerpt: "Learn how to identify and treat black spot disease, a common fungal infection affecting roses and other plants.",
            image: articleImg,
            date: "June 15, 2023",
            views: 34
        },
        {
            id: 102,
            title: "Organic Fungicides for Home Gardens",
            excerpt: "Discover effective organic fungicides that are safe for your garden, family, and the environment.",
            image: articleImg,
            date: "May 22, 2023",
            views: 42
        },
        {
            id: 103,
            title: "Preventing Common Plant Diseases",
            excerpt: "Proactive strategies to keep your plants healthy and disease-free throughout the growing season.",
            image: articleImg,
            date: "April 10, 2023",
            views: 56
        }
    ];

    const handleLikeClick = () => {
        setLiked(!liked);
    };

    const handleCommentSubmit = (e) => {
        e.preventDefault();
        if (commentText.trim()) {
            alert("Comment submitted: " + commentText);
            setCommentText('');
        }
    };

    const handleRelatedArticleClick = (articleId) => {
        navigate(`/articles/articleDetails/${articleId}`);
    };

    return (
        <div className="article-details-page">
            <Header background={true} />

            <div className="article-details-container">
                {/* Breadcrumb */}
                <div className="article-details-breadcrumb">
                    <a href="/">Home</a>
                    <span className="separator">/</span>
                    <a href="/articles">Articles</a>
                    <span className="separator">/</span>
                    <a href="/articles/categories">Plant Diseases</a>
                    <span className="separator">/</span>
                    <span className="current">Powdery Mildew</span>
                </div>

                {/* Article Header */}
                <div className="article-header">
                    <div className="article-info">
                        <h1>{articleStates.title}</h1>
                        <p>A comprehensive guide to understanding and managing {articleStates.title}, a common fungal disease affecting various plants.</p>
                        <div className="article-stats">
                            <span><i className="fas fa-eye"></i> {articleStates.viewsCount} views</span>
                            <span><i className="fas fa-calendar-alt"></i> Updated: {articleStates.date}</span>
                            <span><i className="fas fa-user"></i> By: Dr. Ahmed Mahmoud</span>
                        </div>
                    </div>
                    <div className="article-image">
                        <img src={articleStates.imageUrl} alt={articleStates.title} />
                    </div>
                </div>

                {/* Article Content */}
                <div className="article-content">

                    {article.map((article) => 
                        <div key={article.id} className="article-section">
                            <h2>{article.title}</h2>
                            <p>
                                <Description text={article.content}/>
                            </p>
                        </div>
                        
                        // <div className="article-section">
                        //     <h2>What is Powdery Mildew?</h2>
                        //     <p>
                        //         Powdery mildew is a fungal disease that appears as a white or gray powdery coating on leaves, stems, flowers, and sometimes fruits. It's one of the most common and recognizable plant diseases, affecting a wide range of plants in both indoor and outdoor environments.
                        //     </p>
                        //     <p>
                        //         Unlike many other fungal diseases that thrive in wet conditions, powdery mildew can develop and spread in dry climates, requiring only high humidity and moderate temperatures to establish itself.
                        //     </p>
                        // </div>
                    )}

                    {/* <div className="article-section">
                        <h2>What is Powdery Mildew?</h2>
                        <p>
                            Powdery mildew is a fungal disease that appears as a white or gray powdery coating on leaves, stems, flowers, and sometimes fruits. It's one of the most common and recognizable plant diseases, affecting a wide range of plants in both indoor and outdoor environments.
                        </p>
                        <p>
                            Unlike many other fungal diseases that thrive in wet conditions, powdery mildew can develop and spread in dry climates, requiring only high humidity and moderate temperatures to establish itself.
                        </p>
                    </div>

                    <div className="article-section">
                        <h2>Plants Most Affected</h2>
                        <p>
                            Powdery mildew can affect a wide variety of plants, but some are particularly susceptible:
                        </p>
                        <ul>
                            <li><strong>Vegetables:</strong> Cucumbers, squash, pumpkins, tomatoes, peppers, and beans</li>
                            <li><strong>Fruit Trees:</strong> Apples, grapes, berries, and stone fruits</li>
                            <li><strong>Ornamental Plants:</strong> Roses, zinnias, phlox, lilacs, and many other flowering plants</li>
                            <li><strong>Trees and Shrubs:</strong> Dogwoods, crape myrtles, and azaleas</li>
                        </ul>
                    </div>

                    <div className="article-section">
                        <h2>Causes of the Disease</h2>
                        <p>
                            Several factors contribute to the development and spread of powdery mildew:
                        </p>
                        <ul>
                            <li>High humidity (50-90%) with warm temperatures (15-27°C/60-80°F)</li>
                            <li>Poor air circulation between plants</li>
                            <li>Overcrowded plantings</li>
                            <li>Excess nitrogen fertilizers, leading to soft, susceptible new growth</li>
                            <li>Fungal spores spread through air or contaminated gardening tools</li>
                            <li>Shade or low light conditions</li>
                        </ul>
                        <p>
                            Unlike many other fungal diseases, powdery mildew doesn't require standing water on leaves to infect plants, making it particularly troublesome in both dry and humid climates.
                        </p>
                    </div>

                    <div className="article-section">
                        <h2>Symptoms</h2>
                        <p>
                            Identifying powdery mildew is relatively straightforward due to its distinctive appearance:
                        </p>
                        <ul className="checkmark">
                            <li>White or grayish-white, flour-like coating on leaves, stems, and sometimes fruits</li>
                            <li>Leaf curling, yellowing, and distortion</li>
                            <li>Stunted growth and reduced productivity</li>
                            <li>Premature leaf drop in severe cases</li>
                            <li>Discolored or deformed fruits</li>
                            <li>Infected buds may fail to open</li>
                        </ul>
                        <p>
                            The powdery coating consists of fungal mycelium and spore-producing structures. As the disease progresses, the white patches may turn yellowish-brown and develop small, black, pepper-like dots (cleistothecia), which are the fungal reproductive structures.
                        </p>
                    </div>

                    <div className="article-section">
                        <h2>Prevention</h2>
                        <p>
                            Preventing powdery mildew is often easier than treating it once established:
                        </p>
                        <ul>
                            <li>Plant resistant varieties when available</li>
                            <li>Ensure proper spacing between plants for good air circulation</li>
                            <li>Avoid overhead watering to reduce humidity around plants</li>
                            <li>Water in the morning so plants can dry before evening</li>
                            <li>Prune overcrowded areas to improve airflow</li>
                            <li>Avoid excessive nitrogen fertilization</li>
                            <li>Remove and destroy infected plant parts</li>
                            <li>Clean garden tools after working with infected plants</li>
                            <li>Apply preventive fungicides during periods of high risk</li>
                        </ul>
                        <div className="article-tip">
                            Proper plant spacing and good air circulation are your first line of defense against powdery mildew.
                        </div>
                    </div>

                    <div className="article-section">
                        <h2>Treatment</h2>
                        <p>
                            If prevention fails and powdery mildew appears, several treatment options are available:
                        </p>
                        <ul>
                            <li><strong>Baking Soda Solution:</strong> Mix 1 tablespoon of baking soda, 1 teaspoon of mild liquid soap, and 1 gallon of water. Spray on affected plants weekly.</li>
                            <li><strong>Milk Spray:</strong> Mix 1 part milk to 2-3 parts water and spray on plants. The proteins in milk have antifungal properties.</li>
                            <li><strong>Neem Oil:</strong> An effective organic treatment that also controls many insect pests. Follow label instructions for application.</li>
                            <li><strong>Potassium Bicarbonate:</strong> More effective than baking soda and available in commercial formulations specifically for powdery mildew.</li>
                            <li><strong>Sulfur-based Fungicides:</strong> Effective preventive treatment, but can damage plants in hot weather (above 85°F/29°C).</li>
                            <li><strong>Commercial Fungicides:</strong> Products containing chlorothalonil, myclobutanil, or propiconazole can be effective for severe infections.</li>
                        </ul>
                        <div className="article-tip">
                            Early treatment is crucial! Begin at the first sign of infection for best results. Always test any spray on a small area first to check for plant sensitivity.
                        </div>
                    </div>*/}
                </div> 

                {/* Article Engagement */}
                <div className="article-engagement">
                    <div className="article-actions">
                        <div
                            className={`article-action ${liked ? 'liked' : ''}`}
                            onClick={handleLikeClick}
                        >
                            <i className={`${liked ? 'fas' : 'far'} fa-heart`}></i>
                            <span>{liked ? 'Liked' : 'Like'}</span>
                        </div>
                        <div className="article-action">
                            <i className="far fa-comment"></i>
                            <span>Comment</span>
                        </div>
                        <div className="article-action">
                            <i className="far fa-bookmark"></i>
                            <span>Save</span>
                        </div>
                    </div>
                    <div className="article-share">
                        <div className="share-button facebook">
                            <i className="fab fa-facebook-f"></i>
                        </div>
                        <div className="share-button twitter">
                            <i className="fab fa-twitter"></i>
                        </div>
                        <div className="share-button linkedin">
                            <i className="fab fa-linkedin-in"></i>
                        </div>
                    </div>
                </div>

                {/* Comments Section */}
                <div className="comments-section">
                    <div className="comments-header">
                        <h3>Comments</h3>
                        <div className="comments-count">{comments.length}</div>
                    </div>

                    {comments.map((comment) => (
                        <div key={comment.id} className="comment">
                            <div className="comment-avatar">
                                <img src={comment.avatar} alt={comment.author} />
                            </div>
                            <div className="comment-content">
                                <h4 className="comment-author">{comment.author}</h4>
                                <p className="comment-text">{comment.text}</p>
                                <div className="comment-footer">
                                    <div className="comment-date">
                                        <i className="far fa-clock"></i>
                                        <span>{comment.date}</span>
                                    </div>
                                    <div className="comment-actions">
                                        <div className="comment-action">
                                            <i className="far fa-thumbs-up"></i>
                                            <span>{comment.likes}</span>
                                        </div>
                                        <div className="comment-action">
                                            <i className="far fa-reply"></i>
                                            <span>Reply</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}

                    {/* Add Comment Form */}
                    <div className="add-comment">
                        <div className="add-comment-header">
                            <div className="add-comment-avatar">
                                <img src={userImg} alt="Your Avatar" />
                            </div>
                            <h4 className="add-comment-title">Add a Comment</h4>
                        </div>
                        <form className="comment-form" onSubmit={handleCommentSubmit}>
                            <div className="comment-input">
                                <textarea
                                    placeholder="Share your thoughts..."
                                    value={commentText}
                                    onChange={(e) => setCommentText(e.target.value)}
                                ></textarea>
                            </div>
                            <button type="submit" className="comment-submit">
                                <span>Post Comment</span>
                                <i className="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>

                {/* Related Articles */}
                <div className="related-articles">
                    <div className="related-articles-header">
                        <h3>Related Articles</h3>
                        <a href="/articles" className="view-all-link">
                            <span>View All</span>
                            <i className="fas fa-arrow-right"></i>
                        </a>
                    </div>

                    <div className="related-articles-grid">
                        {relatedArticles.map((article) => (
                            <div
                                key={article.id}
                                className="related-article-card"
                                onClick={() => handleRelatedArticleClick(article.id)}
                            >
                                <div className="related-article-image">
                                    <img src={article.image} alt={article.title} />
                                </div>
                                <div className="related-article-content">
                                    <h4 className="related-article-title">{article.title}</h4>
                                    <p className="related-article-excerpt">{article.excerpt}</p>
                                    <div className="related-article-footer">
                                        <div className="related-article-date">
                                            <i className="far fa-calendar-alt"></i>
                                            <span>{article.date}</span>
                                        </div>
                                        <div className="related-article-views">
                                            <i className="far fa-eye"></i>
                                            <span>{article.views}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* <Footer /> */}
        </div>
    );
}





