export default function PostDescription({ id, title, content, onUpdate, onDelete, onAddAfter }) {
    return(
        <div className="between-flex">
            <div className="post-description">
                <div className="title">
                    <input placeholder="Title" value={title} onChange={(e) => onUpdate(id, "title", e.target.value)}/>
                </div>
                <div className="description">
                    <textarea placeholder="Tell Your Story" value={content} onChange={(e) => onUpdate(id, "content", e.target.value)}></textarea>
                </div>
            </div>
            <div className="post-controls center-flex" style={{flexBasis: '20%', gap: '25px', flexDirection:'column', alignItems: 'flex-end', alignSelf: 'flex-start', paddingTop: '10px'}}>


                <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" fill="#000000" onClick={() => onAddAfter(id)}>
                    <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
                    <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g>
                    <g id="SVGRepo_iconCarrier">
                        <path fill="#d3d1d1" d="M352 480h320a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64z"></path>
                        <path fill="#d3d1d1" d="M480 672V352a32 32 0 1 1 64 0v320a32 32 0 0 1-64 0z"></path>
                        <path fill="#d3d1d1" d="M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"></path>
                    </g>
                </svg>

                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" onClick={() => onDelete(id)}>
                    <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
                    <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g>
                    <g id="SVGRepo_iconCarrier"> 
                        <path d="M16 12L12 12L8 12" stroke="#d3d1d1" strokeWidth="1.5" strokeLinecap="round"></path> 
                        <circle cx="12" cy="12" r="10" stroke="#d3d1d1" strokeWidth="1.5"></circle> 
                    </g>
                </svg>

            </div>
        </div>
    );
}