import React, { useEffect, useState, useMemo } from "react";
import { useParams, useNavigate } from "react-router";
import Header from "../shared/Header";
import Footer from "../shared/Footer";
import "../../assets/styles/category-articles.css";

// Import sample images
import articleImg from '../../assets/images/ArticleBackground.jpg';

export default function CategoryArticlePage() {

    const BASE_URL = process.env.REACT_APP_BASE_URL;
    // console.log(BASE_URL)

    const navigate = useNavigate();
    const { id } = useParams();
    const [articles, setArticles] = useState([]);
    const [loading, setLoading] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
















    // Add a class to the body when the component mounts
    useEffect(() => {
        document.body.classList.add('category-articles-page-active');

        // Clean up function to remove the class when component unmounts
        return () => {
            document.body.classList.remove('category-articles-page-active');
        };
    }, []);

    // Sample articles data using useMemo to prevent unnecessary re-creation
    // const sampleArticles = useMemo(() => [
    //     {
    //         id: 1,
    //         title: "Understanding Apple Scab: Causes, Symptoms, and Treatment",
    //         excerpt: "Apple scab is a potentially serious fungal disease of ornamental and fruit trees in the rose family. Trees that are most commonly and severely affected include crabapple, hawthorn, mountain-ash, apple and pear. In ornamental trees, leaf loss caused by apple scab can make trees unsightly and aesthetically unappealing. In fruit trees, leaf loss can reduce fruit yield. In addition, the presence of apple scab on fruits can make the fruit difficult, if not impossible, to market.",
    //         image: articleImg,
    //         views: 42
    //     },
    //     {
    //         id: 2,
    //         title: "Fire Blight in Apple Trees: Prevention and Control Methods",
    //         excerpt: "Fire blight is a destructive bacterial disease that affects apples, pears, and other members of the rose family. Learn about the symptoms, how it spreads, and effective strategies to prevent and control this devastating disease in your orchard.",
    //         image: articleImg,
    //         views: 35
    //     },
    //     {
    //         id: 3,
    //         title: "Cedar Apple Rust: Managing a Common Fungal Disease",
    //         excerpt: "Cedar apple rust is a fungal disease that requires both juniper and apple trees to complete its life cycle. Discover how to identify this disease, understand its unique life cycle, and implement effective management practices to protect your apple trees.",
    //         image: articleImg,
    //         views: 28
    //     },
    //     {
    //         id: 4,
    //         title: "Powdery Mildew on Apple Trees: Organic Solutions",
    //         excerpt: "Powdery mildew is a common fungal disease that affects many plants, including apple trees. This article explores organic and environmentally friendly approaches to managing powdery mildew without relying on harsh chemicals.",
    //         image: articleImg,
    //         views: 19
    //     },
    //     {
    //         id: 5,
    //         title: "Apple Maggot Control: Protecting Your Harvest",
    //         excerpt: "The apple maggot fly is a serious pest that can devastate apple crops. Learn how to identify these pests, understand their life cycle, and implement effective control measures to protect your apple harvest from damage.",
    //         image: articleImg,
    //         views: 31
    //     },
    //     {
    //         id: 6,
    //         title: "Bitter Pit in Apples: Causes and Prevention",
    //         excerpt: "Bitter pit is a physiological disorder that affects apple quality. This comprehensive guide explains the calcium deficiency that causes bitter pit, how to identify affected fruit, and strategies to prevent this condition in your orchard.",
    //         image: articleImg,
    //         views: 24
    //     }
    // ], []);

    // Effect to load articles when the component mounts or id changes
    useEffect(() => {
        // In a real application, you would fetch data from an API
        // For now, we'll use the sample data
        setLoading(true);
        const timer = setTimeout(() => {



        fetch(`${BASE_URL}get-articles-by-sub-categorie-id?subCategorieId=${id}`, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'ngrok-skip-browser-warning':  '69420'
            }
        })
        .then((response) => response.json())
        .then((data) => {
            console.log(data);
            setArticles(data.data.articles)
        })
        setLoading(false);
    }, 500);



        return () => clearTimeout(timer);
    }, [id]); //

    const handleArticleClick = (articleId) => {
        navigate(`/articles/articleDetails/${articleId}`);
    };

    const handleSearch = (e) => {
        setSearchTerm(e.target.value);
    };


    return (
        <div className="category-articles-page">
            <Header background={true} />

            <div className="category-articles-container">
                {/* Breadcrumb */}
                <div className="category-articles-breadcrumb">
                    <a href="/">Home</a>
                    <span className="separator">/</span>
                    <a href="/articles">Articles</a>
                    <span className="separator">/</span>
                    <a href="/articles/categories">Categories</a>
                    <span className="separator">/</span>
                    <span className="current">Apple Diseases</span>
                </div>

                {/* Category Header */}
                <div className="category-articles-header">
                    <div className="category-articles-info">
                        <h2> Apple Diseases Articles</h2>
                        <p>
                            Explore our comprehensive collection of articles about apple diseases,
                            including identification guides, treatment methods, and prevention strategies.
                        </p>
                        <div className="category-articles-stats">
                            <div className="category-articles-stat">
                                <i className="fas fa-file-alt"></i>
                                <span>{articles.length} Articles</span>
                            </div>
                            <div className="category-articles-stat">
                                <i className="fas fa-eye"></i>
                                <span>179 Views</span>
                            </div>
                            <div className="category-articles-stat">
                                <i className="fas fa-calendar-alt"></i>
                                <span>Updated Weekly</span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Search and Filter Section */}
                <div className="search-filter-section">
                    <div className="search-input-wrapper">
                        <i className="fas fa-search"></i>
                        <input
                            type="text"
                            placeholder="Search articles..."
                            value={searchTerm}
                            onChange={handleSearch}
                        />
                    </div>
                    <button className="filter-button">
                        <i className="fas fa-filter"></i>
                        <span>Filter</span>
                    </button>
                </div>

                {/* Articles Grid */}
                {loading ? (
                    <div className="category-articles-empty">
                        <div className="category-articles-empty-icon">
                            <i className="fas fa-spinner fa-spin"></i>
                        </div>
                        <h3 className="category-articles-empty-title">Loading articles...</h3>
                        <p className="category-articles-empty-text">
                            Please wait while we fetch the latest articles for you.
                        </p>
                    </div>
                ) : articles.length > 0 ? (
                    <div className="category-articles-grid">
                        {articles.map((article) => (
                            <div
                                key={article.id}
                                className="category-article-card"
                                onClick={() => handleArticleClick(article.id)}
                            >
                                <div className="category-article-header">
                                    <img src={article.imageUrl} alt={article.title} />
                                    <div className="category-article-title">
                                        <h3>{article.title}</h3>
                                    </div>
                                </div>
                                <div className="category-article-content">
                                    <p className="category-article-excerpt">
                                        {article.description.length > 150
                                            ? `${article.description.substring(0, 150)}...`
                                            : article.description
                                        }
                                    </p>
                                    <div className="category-article-footer">
                                        <div className="category-article-views">
                                            <i className="fas fa-eye"></i>
                                            <span>{article.viewsCount}</span>
                                        </div>
                                        <button className="read-more-button">
                                            <span>Read More</span>
                                            <i className="fas fa-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="category-articles-empty">
                        <div className="category-articles-empty-icon">
                            <i className="fas fa-search"></i>
                        </div>
                        <h3 className="category-articles-empty-title">No articles found</h3>
                        <p className="category-articles-empty-text">
                            We couldn't find any articles matching your search criteria.
                            Try adjusting your search or browse all articles.
                        </p>
                        <button
                            className="category-articles-empty-button"
                            onClick={() => setSearchTerm("")}
                        >
                            <i className="fas fa-redo"></i>
                            <span>Reset Search</span>
                        </button>
                    </div>
                )}

                {/* Pagination */}
                {articles.length > 0 && (
                    <div className="category-articles-pagination">
                        <button className="pagination-button prev">
                            <i className="fas fa-chevron-left"></i>
                        </button>
                        <button className="pagination-button active">1</button>
                        <button className="pagination-button">2</button>
                        <button className="pagination-button">3</button>
                        <button className="pagination-button next">
                            <i className="fas fa-chevron-right"></i>
                        </button>
                    </div>
                )}
            </div>

            <Footer />
        </div>
    );
}

