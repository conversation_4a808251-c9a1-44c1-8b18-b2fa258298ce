import React, { useEffect } from "react";
import { useNavigate } from "react-router";
import Header from "../shared/Header";
import Footer from "../shared/Footer";
import '../../assets/styles/articles-new.css';

export default function Articles() {
    // Initialize the navigate function
    const navigate = useNavigate();

    // Add a class to the body when the component mounts
    useEffect(() => {
        document.body.classList.add('articles-page-active');

        // Clean up function to remove the class when component unmounts
        return () => {
            document.body.classList.remove('articles-page-active');
        };
    }, []);

    // Sample categories data
    const categories = [
        {
            id: 1,
            title: "Plant Diseases",
            description: "Learn about common plant diseases and effective treatment methods.",
            icon: "fa-virus",
            background: "https://images.unsplash.com/photo-1518531933037-91b2f5f229cc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1332&q=80"
        },
        {
            id: 2,
            title: "Crop Management",
            description: "Techniques for optimal crop growth and yield improvement.",
            icon: "fa-seedling",
            background: "https://images.unsplash.com/photo-1500651230702-0e2d8a49d4ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
        },
        {
            id: 3,
            title: "Sustainable Farming",
            description: "Environmentally responsible farming practices for future generations.",
            icon: "fa-leaf",
            background: "https://images.unsplash.com/photo-1464226184884-fa280b87c399?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
        },
        {
            id: 4,
            title: "Irrigation Techniques",
            description: "Modern and efficient water management systems for agriculture.",
            icon: "fa-water",
            background: "https://images.unsplash.com/photo-**********-04071c5f467b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80"
        },
        {
            id: 5,
            title: "Organic Farming",
            description: "Chemical-free farming methods for healthier produce.",
            icon: "fa-apple-alt",
            background: "https://images.unsplash.com/photo-1523348837708-15d4a09cfac2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
        },
        {
            id: 6,
            title: "Pest Control",
            description: "Strategies to protect crops from harmful insects and pests.",
            icon: "fa-bug",
            background: "https://images.unsplash.com/photo-1530836369250-ef72a3f5cda8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
        }
    ];

    // Sample featured articles data
    const featuredArticles = [
        {
            id: 1,
            title: "Understanding and Treating Powdery Mildew in Plants",
            excerpt: "Powdery mildew is a fungal disease that affects a wide range of plants. Learn how to identify and treat this common problem.",
            category: "Plant Diseases",
            author: {
                name: "Dr. Sarah Johnson",
                avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80"
            },
            date: "June 15, 2023",
            image: "https://images.unsplash.com/photo-1571989569743-3aa1d7d9bcc7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
        },
        {
            id: 2,
            title: "Drip Irrigation Systems: Installation and Maintenance Guide",
            excerpt: "Drip irrigation is one of the most efficient watering methods. This guide covers everything from setup to long-term care.",
            category: "Irrigation",
            author: {
                name: "Michael Chen",
                avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80"
            },
            date: "May 22, 2023",
            image: "https://images.unsplash.com/photo-1622383563227-04401ab4e5ea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80"
        },
        {
            id: 3,
            title: "Crop Rotation Strategies for Small Farms",
            excerpt: "Proper crop rotation can improve soil health and reduce pest problems. Discover effective rotation plans for limited space.",
            category: "Crop Management",
            author: {
                name: "Emily Rodriguez",
                avatar: "https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80"
            },
            date: "April 10, 2023",
            image: "https://images.unsplash.com/photo-1625246333195-78d9c38ad449?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
        },
        {
            id: 4,
            title: "Natural Pest Control Methods for Organic Gardens",
            excerpt: "Keep pests at bay without chemicals. These natural solutions are effective and safe for organic certification.",
            category: "Organic Farming",
            author: {
                name: "David Wilson",
                avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
            },
            date: "March 5, 2023",
            image: "https://images.unsplash.com/photo-1471193945509-9ad0617afabf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
        }
    ];

    const handleCategoryClick = (id) => {
        console.log(`Clicked on category with id: ${id}`);
        // Navigate to category page
        navigate(`/articles/categories/subCategories/${id}`);
    };

    const handleArticleClick = (id) => {
        console.log(`Clicked on article with id: ${id}`);
        // Navigate to article details page
        navigate(`/articles/articleDetails/${id}`);
    };

    return (
        <div className="articles-page">
            <Header background={true} />

            <div className="articles-container">
                {/* Hero Section */}
                <section className="articles-hero">
                    <div className="articles-hero-content">
                        <h1>Agricultural Knowledge Hub</h1>
                        <p>Explore our comprehensive collection of articles on farming techniques, plant diseases, and sustainable agriculture</p>
                    </div>
                    <div className="articles-hero-overlay"></div>
                </section>

                {/* Breadcrumb */}
                <div className="articles-breadcrumb">
                    <a href="/">Home</a>
                    <span className="separator">/</span>
                    <span className="current">Articles</span>
                </div>

                {/* Search and Filter Section */}
                <div className="articles-search-section">
                    <div className="articles-search-input">
                        <i className="fas fa-search"></i>
                        <input type="text" placeholder="Search for articles..." />
                    </div>

                    <div className="articles-filter-buttons">
                        <button className="articles-filter-button active">
                            <i className="fas fa-th-large"></i>
                            <span>All</span>
                        </button>
                        <button className="articles-filter-button">
                            <i className="fas fa-filter"></i>
                            <span>Filter</span>
                        </button>
                        <button className="articles-filter-button">
                            <i className="fas fa-sort-amount-down"></i>
                            <span>Sort</span>
                        </button>
                    </div>
                </div>

                {/* Categories Section */}
                <section className="articles-categories-section">
                    <div className="section-header">
                        <h2>Explore Article Categories</h2>
                        <p>Browse through our comprehensive collection of agricultural knowledge organized by topics</p>
                    </div>

                    <div className="articles-categories-grid">
                        {categories.map((category) => (
                            <div
                                key={category.id}
                                className="category-card"
                                onClick={() => handleCategoryClick(category.id)}
                            >
                                <div
                                    className="category-card-bg"
                                    style={{ backgroundImage: `url(${category.background})` }}
                                ></div>
                                <div className="category-card-overlay"></div>
                                <div className="category-card-content">
                                    <div className="category-card-icon">
                                        <i className={`fas ${category.icon}`}></i>
                                    </div>
                                    <h3 className="category-card-title">{category.title}</h3>
                                    <p className="category-card-description">{category.description}</p>
                                    <div className="category-card-link">
                                        <span>Explore Articles</span>
                                        <i className="fas fa-arrow-right"></i>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </section>

                {/* Featured Articles Section */}
                <section className="featured-articles-section">
                    <div className="section-header">
                        <h2>Featured Articles</h2>
                        <p>Our most popular and informative content selected by our agricultural experts</p>
                    </div>

                    <div className="featured-articles-grid">
                        {featuredArticles.map((article) => (
                            <div
                                key={article.id}
                                className="featured-article-card"
                                onClick={() => handleArticleClick(article.id)}
                            >
                                <div className="featured-article-image">
                                    <img src={article.image} alt={article.title} />
                                </div>
                                <div className="featured-article-content">
                                    <span className="featured-article-category">{article.category}</span>
                                    <h3 className="featured-article-title">{article.title}</h3>
                                    <p className="featured-article-excerpt">{article.excerpt}</p>
                                    <div className="featured-article-meta">
                                        <div className="featured-article-author">
                                            <img src={article.author.avatar} alt={article.author.name} />
                                            <span>{article.author.name}</span>
                                        </div>
                                        <div className="featured-article-date">
                                            <i className="far fa-calendar-alt"></i>
                                            <span>{article.date}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </section>

                {/* Newsletter Section */}
                <section className="newsletter-section">
                    <div className="newsletter-bg-pattern"></div>
                    <div className="newsletter-content">
                        <h2>Stay Updated with Agricultural Insights</h2>
                        <p>Subscribe to our newsletter to receive the latest articles, farming tips, and agricultural news directly in your inbox.</p>
                        <div className="newsletter-form">
                            <input type="email" placeholder="Enter your email address" />
                            <button>Subscribe</button>
                        </div>
                    </div>
                </section>
            </div>

            <Footer />
        </div>
    );
}
