/* Authentication-related styles */

.auth-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f8fafc;
}

.auth-loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(20, 83, 45, 0.2);
  border-radius: 50%;
  border-top-color: #14532d;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

.auth-loading p {
  font-size: 1rem;
  color: #64748b;
  font-family: var(--font-primary);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Additional styles for auth-related components */
.auth-error {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
}

.auth-error i {
  font-size: 1.2rem;
}

.auth-success {
  background-color: #dcfce7;
  color: #166534;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
}

.auth-success i {
  font-size: 1.2rem;
}
