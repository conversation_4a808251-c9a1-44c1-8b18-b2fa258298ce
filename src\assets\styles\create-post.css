/* Modern Create Post Page Styles */

/* Fix for header transparency */
.create-post-page-active .modern-header {
  background-color: rgba(20, 83, 45, 0.95); /* Darker forest green with transparency */
  box-shadow: 0 4px 20px rgba(20, 83, 45, 0.2);
  z-index: 1000;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  animation: fadeInDown 0.5s ease-out;
}

.create-post-page-active .modern-header .logo-text,
.create-post-page-active .modern-header .nav-links li a,
.create-post-page-active .modern-header .search-button {
  color: var(--text-light);
}

.create-post-page-active .modern-header .nav-links li a:hover {
  color: white;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.create-post-page-active .modern-header .search-input-wrapper {
  background-color: rgba(255, 255, 255, 0.15);
}

.create-post-page-active .modern-header .search-input {
  color: var(--text-light);
}

.create-post-page-active .modern-header .search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

/* Scrolled header on create post page */
.create-post-page-active .modern-header.scrolled {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(20, 83, 45, 0.1);
  box-shadow: 0 4px 20px rgba(20, 83, 45, 0.1);
}

.create-post-page-active .modern-header.scrolled .logo-text,
.create-post-page-active .modern-header.scrolled .nav-links li a,
.create-post-page-active .modern-header.scrolled .search-button {
  color: var(--text-dark);
}

.create-post-page-active .modern-header.scrolled .search-input-wrapper {
  background-color: var(--background-alt);
}

.create-post-page-active .modern-header.scrolled .search-input {
  color: var(--text-dark);
}

.create-post-page-active .modern-header.scrolled .search-input::placeholder {
  color: rgba(44, 62, 80, 0.7);
}

.create-post-page-active .modern-header.scrolled .nav-links li a:hover {
  color: var(--primary);
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-create-post {
  padding-top: 0;
  min-height: 100vh;
  background-color: #f8fafc;
  font-family: var(--font-primary);
}

.create-post-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 120px 20px 60px;
}

.create-post-header {
  text-align: center;
  margin-bottom: 40px;
}

.create-post-header h1 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-dark);
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

.create-post-header h1::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: #14532d;
  border-radius: 2px;
}

.create-post-header p {
  font-size: var(--text-md);
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: var(--leading-relaxed);
}

/* Category Select Boxes */
.category-selects-wrapper {
  margin-bottom: 30px;
  display: flex;
  gap: 20px;
}

.category-select-container {
  flex: 1;
  position: relative;
}

.category-select-label {
  display: block;
  margin-bottom: 10px;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-dark);
}

.category-select {
  width: 100%;
  padding: 15px;
  font-size: 1rem;
  color: var(--text-dark);
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  appearance: none;
  cursor: pointer;
}

.category-select:focus {
  outline: none;
  border-color: #14532d;
  box-shadow: 0 0 0 3px rgba(20, 83, 45, 0.1);
}

.category-select-container::after {
  content: '\f107';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  right: 15px;
  bottom: 15px;
  color: #64748b;
  pointer-events: none;
}

.category-select option {
  padding: 10px;
}

.category-select:disabled {
  background-color: #f1f5f9;
  cursor: not-allowed;
  color: #94a3b8;
}

/* Main Title Input */
.main-title-wrapper {
  margin-bottom: 40px;
  position: relative;
}

.modern-main-title {
  width: 100%;
  padding: 20px;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-dark);
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.modern-main-title:focus {
  outline: none;
  border-color: #14532d;
  box-shadow: 0 0 0 3px rgba(20, 83, 45, 0.1);
}

.modern-main-title::placeholder {
  color: #cbd5e1;
  font-weight: 400;
}

/* File Upload */
.modern-file-upload {
  margin-bottom: 40px;
  background-color: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.file-upload-header {
  margin-bottom: 20px;
}

.file-upload-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0 0 10px 0;
}

.file-upload-header p {
  font-size: 0.95rem;
  color: #64748b;
  margin: 0;
}

.file-upload-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  padding: 20px;
  border: 2px dashed #e2e8f0;
  border-radius: 8px;
  background-color: #f8fafc;
  transition: all 0.3s ease;
}

.file-upload-area:hover {
  border-color: #14532d;
  background-color: rgba(20, 83, 45, 0.05);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.file-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(20, 83, 45, 0.1);
  color: #14532d;
  border-radius: 8px;
}

.file-name {
  font-size: 0.95rem;
  color: #64748b;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.modern-file-upload-btn {
  padding: 10px 20px;
  background-color: #14532d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-file-upload-btn:hover {
  background-color: #166534;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(20, 83, 45, 0.2);
}

.modern-file-upload-btn i {
  font-size: 1rem;
}

/* Section Styles */
.post-sections {
  margin-bottom: 40px;
}

.post-section {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  margin-bottom: 30px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.post-section:hover {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.section-number {
  font-size: 0.9rem;
  font-weight: 600;
  color: #14532d;
  background-color: rgba(20, 83, 45, 0.1);
  padding: 4px 10px;
  border-radius: 20px;
}

.section-controls {
  display: flex;
  gap: 15px;
}

.section-control-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 1px solid #e2e8f0;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
}

.section-control-btn:hover {
  background-color: #14532d;
  color: white;
  border-color: #14532d;
}

.section-control-btn.delete:hover {
  background-color: #ef4444;
  border-color: #ef4444;
}

.section-content {
  padding: 20px;
}

.section-title-input {
  width: 100%;
  padding: 15px;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-dark);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.section-title-input:focus {
  outline: none;
  border-color: #14532d;
  box-shadow: 0 0 0 3px rgba(20, 83, 45, 0.1);
}

.section-content-textarea {
  width: 100%;
  min-height: 200px;
  padding: 15px;
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-dark);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  resize: vertical;
  transition: all 0.3s ease;
}

.section-content-textarea:focus {
  outline: none;
  border-color: #14532d;
  box-shadow: 0 0 0 3px rgba(20, 83, 45, 0.1);
}

/* Add Section Button */
.add-section-btn {
  width: 100%;
  padding: 15px;
  background-color: white;
  border: 2px dashed #e2e8f0;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 1rem;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 40px;
}

.add-section-btn:hover {
  border-color: #14532d;
  color: #14532d;
  background-color: rgba(20, 83, 45, 0.05);
}

.add-section-btn i {
  font-size: 1.25rem;
}

/* Publish Button */
.publish-container {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.modern-publish-btn {
  padding: 15px 40px;
  background: linear-gradient(135deg, #14532d, #166534);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 4px 15px rgba(20, 83, 45, 0.3);
}

.modern-publish-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(20, 83, 45, 0.4);
}

.modern-publish-btn i {
  font-size: 1.2rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .create-post-header h1 {
    font-size: 2rem;
  }

  .category-selects-wrapper {
    flex-direction: column;
    gap: 15px;
  }

  .modern-main-title {
    font-size: 1.5rem;
    padding: 15px;
  }

  .file-upload-area {
    flex-direction: column;
    align-items: flex-start;
  }

  .file-info {
    margin-bottom: 15px;
  }

  .modern-file-upload-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .create-post-header h1 {
    font-size: 1.75rem;
  }

  .create-post-header p {
    font-size: 1rem;
  }

  .category-select {
    padding: 12px;
    font-size: 0.95rem;
  }

  .category-select-label {
    font-size: 0.95rem;
  }

  .section-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .section-controls {
    align-self: flex-end;
  }
}
