/* Login and Register Page Styles */

.login-page,
.register-page {
  padding: 20px;
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center; /* Changed from space-between to center */
  align-items: center;
  position: relative;
  transition: .3s;
  background-color: white; /* Changed to white background */
  gap: 20px; /* Add a small gap between form and image */
  max-width: 1200px;
  margin: 0 auto;
}

/* Image container styles */
.login-page .center-flex,
.register-page .center-flex {
  max-height: 90vh;
  overflow: hidden;
}

.login-page .center-flex img,
.register-page .center-flex img {
  object-fit: cover;
  max-height: 90vh;
}

.login-logo,
.register-logo {
  position: absolute;
  width: 50px;
}

.login-logo {
  left: 0px;
  top: 20px;
}

.register-logo {
  left: 100px;
  top: 40px;
}

.login-form,
.register-form {
  font-family: "Poppins", sans-serif;
}

.login-form > h1,
.register-form > h1 {
  margin: 0;
  font-weight: normal;
  font-size: 40px;
}

.login-form > h1 + p,
.register-form > h1 + p {
  color: #939393;
  margin-top: -5px;
  margin-bottom: 50px;
  font-weight: normal;
  font-size: 18px;
}

.login-form input,
.register-form input {
  display: block;
  width: calc(100% - 40px);
  padding: 18px 20px;
  margin-right: auto;
  border-radius: 25px 0 0 25px;
  border: 1px solid #939393;
  border-right: none;
  outline: none;
  color: #333;
  letter-spacing: 1px;
  font-size: 17px;
  position: relative;
  max-width: 350px;
}

.login-form input::placeholder,
.register-form input::placeholder {
  font-size: 14px;
  color: #939393;
}

.login-form input + div,
.register-form input + div {
  padding: 16px 20px;
  margin-right: auto;
  border-radius: 0 25px 25px 0;
  border: 1px solid #939393;
  border-left: none;
  color: #939393;
}

.login-form input + div i,
.register-form input + div i {
  width: 20px;
}

/* Original button styles */
.login-form .reg-btn,
.login-form .login-btn,
.register-form .reg-btn {
  margin-top: 30px;
  padding: 10px 20px;
  color: white;
  background-color: #22766e;
  border: none;
  outline: none;
  font-size: 17px;
  border-radius: 7px;
  cursor: pointer;
}

.login-form .options-head,
.register-form .options-head {
  position: relative;
  color: #939393;
  margin: 30px auto;
  font-size: 14px;
  padding: 0 10px;
  background-color: white;
  width: fit-content;
}

.login-form .options-head::before,
.register-form .options-head::before,
.login-form .options-head::after,
.register-form .options-head::after {
  content: '';
  width: 100px;
  height: 1px;
  background-color: #939393;
  position: absolute;
  top: 50%;
}

.options-head::before {
  left: calc(0px - 100px);
}

.options-head::after {
  right: calc(0px - 100px);
}

.sign-with-acc {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  margin-bottom: 32px;
}

.sign-with-acc svg {
  width: 40px;
  cursor: pointer;
}

.svg-container {
  width: 60px;
  height: 60px;
  border: 1px solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.svg-container:hover {
  border-color: #059669;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Responsive styles */
@media (max-width: 992px) {
  .login-page,
  .register-page {
    gap: 10px;
  }

  .login-form,
  .register-form {
    padding: 0 10px;
  }
}

@media (max-width: 768px) {
  .login-page,
  .register-page {
    flex-direction: column;
    justify-content: center;
    gap: 30px;
    padding: 60px 20px;
    overflow-y: auto;
    height: auto;
    min-height: 100vh;
  }

  .login-form > h1,
  .register-form > h1 {
    font-size: 32px;
  }

  .login-form > h1 + p,
  .register-form > h1 + p {
    font-size: 16px;
    margin-bottom: 30px;
  }

  .center-flex[style*="flexBasis"] {
    flex-basis: auto !important;
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
  }

  .center-flex img[alt="logo"] {
    height: auto !important;
    max-height: 250px;
    width: auto;
    max-width: 100%;
    object-fit: contain;
  }
}
