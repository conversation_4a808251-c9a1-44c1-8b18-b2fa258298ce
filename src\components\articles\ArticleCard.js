import Logo from "./Logo";
import logoImg from './imgs/logoImg.png';

import { useNavigate } from "react-router";
import img from './imgs/Powdery Mildew_ Treatment and Control on Plants.jpg'

export default function ArticleCard({category}) {

    const navigate = useNavigate();
    const handleClick = () => {
      //navigate(`/articles/categories/subCategories/${category.id}`);
        navigate(`/articles/categories/subCategories/1`);
    };
    return (
        <div className="article-card" onClick={handleClick}>
           
            <div className='img-wrapper circle'>
                <img className="circle article-img" src={img} alt="img"  />
            </div>
            <div className='between-flex' style={{color: 'white',padding: '10px 15px', fontSize: '18px', position: 'absolute', bottom: '0', left: '0', width: '100%'}}>
                <span>Powdery Mildew</span> 
                <i className = "fa-regular fa-circle-right"></i>
            </div>
            
        </div>
    );
}

















// with api

// import img from './imgs/Powdery Mildew_ Treatment and Control on Plants.jpg'

// import { useNavigate } from "react-router";

// export default function ArticleCard({category}) {
//     const navigate = useNavigate();

//     const handleClick = () => {
//         navigate(`/articles/categories/subCategories/${category.id}`);
//         // navigate("/articles/categories/subCategories/1");
//     };
  
//     return (
//         <div className="article-card" onClick={handleClick}>
           
//             <div className='img-wrapper circle'>
//                 <img src={category.imageUrl}  className="circle article-img" alt="img"  />
//             </div>
//             <div className='between-flex' style={{color: 'white',padding: '10px 15px', fontSize: '18px', position: 'absolute', bottom: '0', left: '0', width: '100%'}}>
//                 <span>{category.name}</span> 
//                 <i className="fa-regular fa-circle-right"></i>
//             </div>
            
//         </div>
//     );
// }