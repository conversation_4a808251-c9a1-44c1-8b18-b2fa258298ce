/* Modern Contact Page Styles */

.modern-contact-page {
  padding-top: 0;
  font-family: 'Inter', sans-serif;
  color: var(--text-dark);
}

/* Fix for header transparency */
.contact-page-active .modern-header {
  background-color: rgba(6, 78, 59, 0.95); /* Dark green with transparency */
  box-shadow: 0 4px 20px rgba(6, 78, 59, 0.2);
  z-index: 1000;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  animation: fadeInDown 0.5s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.contact-page-active .modern-header .logo-text,
.contact-page-active .modern-header .nav-links li a,
.contact-page-active .modern-header .search-button {
  color: var(--text-light);
}

.contact-page-active .modern-header .nav-links li a:hover {
  color: white;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.contact-page-active .modern-header .search-input-wrapper {
  background-color: rgba(255, 255, 255, 0.15);
}

.contact-page-active .modern-header .search-input {
  color: var(--text-light);
}

.contact-page-active .modern-header .search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

/* Scrolled header on contact page */
.contact-page-active .modern-header.scrolled {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(6, 78, 59, 0.1);
  box-shadow: 0 4px 20px rgba(6, 78, 59, 0.1);
}

.contact-page-active .modern-header.scrolled .logo-text,
.contact-page-active .modern-header.scrolled .nav-links li a,
.contact-page-active .modern-header.scrolled .search-button {
  color: var(--text-dark);
}

.contact-page-active .modern-header.scrolled .search-input-wrapper {
  background-color: var(--background-alt);
}

.contact-page-active .modern-header.scrolled .search-input {
  color: var(--text-dark);
}

.contact-page-active .modern-header.scrolled .search-input::placeholder {
  color: rgba(44, 62, 80, 0.7);
}

.contact-page-active .modern-header.scrolled .nav-links li a:hover {
  color: var(--primary);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Hero Section */
.contact-hero {
  background: linear-gradient(135deg, #064e3b, #10b981);
  min-height: calc(100vh - 80px); /* 100vh minus header height */
  display: flex;
  align-items: center;
  padding: 0;
  color: white;
  position: relative;
  overflow: hidden;
}

.contact-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="0.05" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,250.7C960,235,1056,181,1152,165.3C1248,149,1344,171,1392,181.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-size: cover;
  background-position: center bottom;
  opacity: 0.3;
  z-index: 0;
}

.contact-hero-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  gap: 40px;
}

.contact-hero-text {
  flex: 1;
  text-align: left;
  animation: fadeInLeft 1s ease-out;
}

.contact-hero h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  position: relative;
  display: inline-block;
}

.contact-hero h1::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 80px;
  height: 4px;
  background-color: #34d399; /* Lighter green accent */
  border-radius: 2px;
}

.contact-hero p {
  font-size: 1.2rem;
  max-width: 600px;
  margin-bottom: 40px;
  opacity: 0.9;
}

.contact-hero-stats {
  display: flex;
  gap: 30px;
  margin-top: 40px;
}

.hero-stat {
  display: flex;
  flex-direction: column;
  animation: fadeInUp 0.8s ease-out;
  animation-fill-mode: both;
}

.hero-stat:nth-child(1) { animation-delay: 0.2s; }
.hero-stat:nth-child(2) { animation-delay: 0.4s; }
.hero-stat:nth-child(3) { animation-delay: 0.6s; }

.hero-stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #34d399; /* Lighter green accent */
  line-height: 1;
}

.hero-stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-top: 5px;
}

.contact-hero-image {
  flex: 1;
  position: relative;
  height: 400px;
  animation: fadeInRight 1s ease-out;
}

.contact-hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
  transition: all 0.5s ease;
}

.contact-hero-image:hover img {
  transform: scale(1.02) translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.hero-image-shape {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 100%;
  height: 100%;
  border: 3px solid #34d399; /* Lighter green accent */
  border-radius: 20px;
  z-index: 0;
  animation: shapeFloat 6s ease-in-out infinite;
}

@keyframes shapeFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

/* Contact Container */
.contact-container {
  display: flex;
  flex-wrap: wrap;
  max-width: 1200px;
  margin: 0 auto;
  padding: 80px 20px;
  gap: 40px;
}

/* Contact Info Section */
.contact-info-section {
  flex: 1;
  min-width: 300px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.contact-info-card {
  background-color: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.contact-info-card:nth-child(1) {
  animation-delay: 0.1s;
}

.contact-info-card:nth-child(2) {
  animation-delay: 0.2s;
}

.contact-info-card:nth-child(3) {
  animation-delay: 0.3s;
}

.contact-info-card:nth-child(4) {
  animation-delay: 0.4s;
}

.contact-info-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.contact-info-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
  font-size: 24px;
  box-shadow: 0 10px 20px rgba(5, 150, 105, 0.2);
  transition: all 0.3s ease;
}

.contact-info-card:hover .contact-info-icon {
  transform: rotate(360deg);
  background: linear-gradient(135deg, var(--secondary), var(--primary));
}

.contact-info-card h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-dark);
}

.contact-info-card p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* Contact Form Section */
.contact-form-section {
  flex: 1;
  min-width: 300px;
}

.contact-form-container {
  background-color: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  animation: fadeInRight 0.8s ease-out;
}

.contact-form-container h2 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 30px;
  color: var(--text-dark);
  text-align: center;
}

.form-message {
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
  font-weight: 500;
}

.form-message.success {
  background-color: rgba(5, 150, 105, 0.1);
  color: var(--primary);
  border: 1px solid rgba(5, 150, 105, 0.2);
}

.form-message.error {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-dark);
}

.form-group label i {
  color: var(--primary);
  margin-right: 8px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 15px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: #f8fafc;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
  background-color: white;
}

.form-group textarea {
  resize: vertical;
  min-height: 150px;
}

.submit-button {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  animation: pulse 2s infinite;
}

.submit-button:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary));
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(5, 150, 105, 0.2);
}

.submit-button:active {
  transform: translateY(0);
}

/* Map Section */
.contact-map-section {
  padding: 0;
  margin: 0;
}

.map-container {
  width: 100%;
  height: 450px;
  overflow: hidden;
  position: relative;
}

.map-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 10px;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), transparent);
  z-index: 1;
}

/* Social Section */
.contact-social-section {
  background-color: #f8fafc;
  padding: 80px 20px;
  text-align: center;
}

.social-container {
  max-width: 800px;
  margin: 0 auto;
}

.social-container h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: var(--text-dark);
}

.social-container p {
  color: #666;
  margin-bottom: 40px;
  font-size: 1.1rem;
}

.social-icons {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.social-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  transition: all 0.3s ease;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.5s ease-out;
  animation-fill-mode: both;
}

.social-icon:nth-child(1) { animation-delay: 0.1s; }
.social-icon:nth-child(2) { animation-delay: 0.2s; }
.social-icon:nth-child(3) { animation-delay: 0.3s; }
.social-icon:nth-child(4) { animation-delay: 0.4s; }
.social-icon:nth-child(5) { animation-delay: 0.5s; }
.social-icon:nth-child(6) { animation-delay: 0.6s; }

.social-icon:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.social-icon.facebook {
  background: #1877f2;
}

.social-icon.twitter {
  background: #1da1f2;
}

.social-icon.instagram {
  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
}

.social-icon.linkedin {
  background: #0a66c2;
}

.social-icon.youtube {
  background: #ff0000;
}

.social-icon.telegram {
  background: #0088cc;
}

/* Partners Section */
.contact-partners-section {
  background-color: #f8fafc;
  padding: 80px 20px;
  text-align: center;
  position: relative;
}

.partners-container {
  max-width: 1200px;
  margin: 0 auto;
  animation: fadeInUp 0.8s ease-out;
}

.partners-container h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: var(--text-dark);
}

.partners-container p {
  font-size: 1.1rem;
  margin-bottom: 50px;
  color: #64748b;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.partners-logos {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 40px;
  margin-top: 30px;
}

.partner-logo {
  flex: 0 0 auto;
  padding: 20px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 180px;
  height: 100px;
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.partner-logo:nth-child(1) { animation-delay: 0.1s; }
.partner-logo:nth-child(2) { animation-delay: 0.2s; }
.partner-logo:nth-child(3) { animation-delay: 0.3s; }
.partner-logo:nth-child(4) { animation-delay: 0.4s; }
.partner-logo:nth-child(5) { animation-delay: 0.5s; }

.partner-logo:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.partner-logo img {
  max-width: 100%;
  max-height: 60px;
  filter: grayscale(100%);
  opacity: 0.7;
  transition: all 0.3s ease;
}

.partner-logo:hover img {
  filter: grayscale(0%);
  opacity: 1;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .contact-hero {
    padding: 40px 0;
  }

  .contact-hero-content {
    flex-direction: column;
    text-align: center;
    padding: 40px 20px;
  }

  .contact-hero-text {
    text-align: center;
    margin-bottom: 40px;
  }

  .contact-hero h1 {
    font-size: 2.8rem;
  }

  .contact-hero h1::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .contact-hero-stats {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .contact-hero h1 {
    font-size: 2.5rem;
  }

  .contact-hero p {
    font-size: 1rem;
  }

  .contact-hero-image {
    height: 300px;
  }

  .contact-container {
    padding: 40px 20px;
  }

  .contact-form-container {
    padding: 30px 20px;
  }

  .social-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .partners-container h2 {
    font-size: 2rem;
  }

  .partners-container p {
    font-size: 1rem;
  }

  .partners-logos {
    gap: 20px;
  }

  .partner-logo {
    width: 150px;
    height: 80px;
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .contact-hero {
    min-height: auto;
  }

  .contact-hero-content {
    padding: 30px 15px;
  }

  .contact-hero h1 {
    font-size: 2rem;
  }

  .contact-hero-stats {
    flex-direction: column;
    gap: 15px;
    align-items: center;
  }

  .contact-hero-image {
    height: 250px;
  }

  .contact-info-section {
    grid-template-columns: 1fr;
  }

  .social-icons {
    gap: 15px;
  }

  .social-icon {
    width: 45px;
    height: 45px;
    font-size: 18px;
  }

  .partners-container h2 {
    font-size: 1.8rem;
  }

  .partners-logos {
    gap: 15px;
  }

  .partner-logo {
    width: 130px;
    height: 70px;
    padding: 10px;
  }
}
