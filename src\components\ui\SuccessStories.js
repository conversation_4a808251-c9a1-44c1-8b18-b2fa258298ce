import React from 'react';

export default function SuccessStories() {
  // Static success stories data
  const stories = [
    {
      id: 1,
      name: "<PERSON>",
      location: "Fayoum, Egypt",
      image: "https://images.unsplash.com/photo-1520052203542-d3095f1b6cf0?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
      quote: "By implementing organic farming techniques, I increased my crop yield by 30% while reducing water usage.",
      achievement: "Sustainable Farming"
    },
    {
      id: 2,
      name: "<PERSON><PERSON>oud",
      location: "Alexandria, Egypt",
      image: "https://images.unsplash.com/photo-1594749794743-c81176a0b1ff?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
      quote: "<PERSON><PERSON><PERSON><PERSON><PERSON>'s soil analysis helped me identify the perfect crops for my land. My income has doubled in just one year!",
      achievement: "Yield Optimization"
    },
    {
      id: 3,
      name: "<PERSON>",
      location: "Aswan, Egypt",
      image: "https://images.unsplash.com/photo-1622127922040-13cab637ee78?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
      quote: "Switching to drip irrigation based on Mazraaty's recommendation saved me 40% on water costs while improving crop health.",
      achievement: "Water Conservation"
    },
    {
      id: 4,
      name: "Layla Ibrahim",
      location: "Luxor, Egypt",
      image: "https://images.unsplash.com/photo-1621905252507-b35492cc74b4?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
      quote: "The pest management strategies I learned helped me eliminate chemical pesticides completely. My produce is now certified organic!",
      achievement: "Organic Certification"
    }
  ];

  return (
    <div className="success-stories-section">
      <div className="success-stories-container">
        <div className="success-stories-header">
          <h2>Farming Success Stories</h2>
          <p>Real farmers sharing real results from across Egypt</p>
        </div>
        
        <div className="success-stories-grid">
          {stories.map(story => (
            <div className="success-story-card" key={story.id}>
              <div className="story-image-container">
                <img src={story.image} alt={story.name} className="story-image" />
                <div className="story-achievement">{story.achievement}</div>
              </div>
              <div className="story-content">
                <div className="story-quote">
                  <i className="fa-solid fa-quote-left quote-icon"></i>
                  <p>{story.quote}</p>
                </div>
                <div className="story-author">
                  <h4>{story.name}</h4>
                  <p>{story.location}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="success-stories-cta">
          <button className="modern-button primary">
            View More Success Stories
            <span className="button-icon">
              <i className="fa-solid fa-arrow-right"></i>
            </span>
          </button>
        </div>
      </div>
    </div>
  );
}
