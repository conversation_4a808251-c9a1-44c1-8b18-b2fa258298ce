import { Link } from "react-router";

export default function Pages({ mobile }) {
    return (
        <ul className={`nav-links ${mobile ? 'mobile-links' : ''}`}>
            <li>
                <Link to="/#" style={{"--nav-item-index": 0}}>
                    {mobile && <i className="fa-solid fa-home"></i>}
                    <span>Home</span>
                </Link>
            </li>
            <li>
                <Link to="/articles/categories" style={{"--nav-item-index": 1}}>
                    {mobile && <i className="fa-solid fa-newspaper"></i>}
                    <span>Articles</span>
                </Link>
            </li>
            <li>
                <Link to="/contact" style={{"--nav-item-index": 2}}>
                    {mobile && <i className="fa-solid fa-envelope"></i>}
                    <span>Contact Us</span>
                </Link>
            </li>
            <li>
                <Link to="/about" style={{"--nav-item-index": 3}}>
                    {mobile && <i className="fa-solid fa-info-circle"></i>}
                    <span>About Us</span>
                </Link>
            </li>
        </ul>
    );
}