// export function reg(name, username, password) {




//     let regFormData = new FormData();
//     regFormData.append("name", name);
//     regFormData.append("username", username);
//     regFormData.append("password", password);
//     // regFormData.append("image", img);






//     fetch("https://tarmeezacademy.com/api/v1/register", {
//         method: "POST",
//         body: regFormData, // لا تحتاج إلى تحويل البيانات عند استخدام FormData
//         headers: {
//             // لا تضف "Content-Type" عند استخدام FormData، لأنه يتم تحديده تلقائيًا
//         }
//     })
//     .then((response) => {
//         console.log(response)
//     })
//     .catch((response) => {
//         console.log(response);
//     })
    
// }
