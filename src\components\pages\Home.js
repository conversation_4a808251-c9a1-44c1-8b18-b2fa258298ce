import CategoryArticleCard from '../articles/CategoryArticleCard';
import Footer from '../shared/Footer';
import Header from '../shared/Header';
import Landing from '../layout/Landing';
import ReadMoreBtn from '../ui/ReadMoreBtn';
import BackgroundVideo from '../layout/BackgroundVideo';
import SimpleCardSlider from '../ui/SimpleCardSlider';
import SuccessStories from '../ui/SuccessStories';
import PlantHealthSection from '../ui/PlantHealthSection';
import ProjectDiagram from '../ui/ProjectDiagram';
import { useState, useEffect } from 'react';
import '../../assets/styles/success-stories.css';
import '../../assets/styles/project-diagram.css';

export default function Home() {
    const BASE_URL = process.env.REACT_APP_BASE_URL;
    const [articles, setArticles] = useState([]);

    useEffect(() => {
        fetch(`${BASE_URL}get-random`, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'ngrok-skip-browser-warning':  '69420'
            }
        })
        .then(response => response.json())
        .then(data => {
            console.log(data.data.articles);
            setArticles(data.data.articles)
        })
        .catch(error => console.error('Error fetching data:', error));
    }, []);

    return (
        <>
            <Header background={false} />
            <Landing />
            <PlantHealthSection />
            <div className='back-image'>
                <BackgroundVideo />
            </div>

            {/* Success Stories Section */}
            <SuccessStories />

            {/* Card Slider Section */}
                <SimpleCardSlider
                    cardData={articles}
                    title="Agricultural Articles and Topics"
                    subtitle="Explore our curated selection of important agricultural topics and best practices for sustainable farming."
                />

            {/* Project Diagram Section - Comprehensive explanation of the platform */}
            <ProjectDiagram />

            <div style={{width:'100%', backgroundColor:'white'}}>
                <Footer/>
            </div>
        </>
    );
}
