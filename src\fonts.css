/* Unified Font System for the Website */

/* Import Inter font with various weights */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Import Merriweather for headings (optional) */
@import url('https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700;900&display=swap');

/* Import Lora for article content (optional) */
@import url('https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap');

:root {
  /* Font Families */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-heading: 'Merriweather', Georgia, 'Times New Roman', serif;
  --font-content: '<PERSON>ra', Georgia, 'Times New Roman', serif;
  
  /* Font Sizes */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-md: 1.125rem;   /* 18px */
  --text-lg: 1.25rem;    /* 20px */
  --text-xl: 1.5rem;     /* 24px */
  --text-2xl: 1.875rem;  /* 30px */
  --text-3xl: 2.25rem;   /* 36px */
  --text-4xl: 3rem;      /* 48px */
  
  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  
  /* Line Heights */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  /* Letter Spacing */
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;
}

/* Base Typography */
body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  color: var(--text-dark);
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-primary);
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
  margin-top: 0;
}

h1 {
  font-size: var(--text-4xl);
}

h2 {
  font-size: var(--text-3xl);
}

h3 {
  font-size: var(--text-2xl);
}

h4 {
  font-size: var(--text-xl);
}

h5 {
  font-size: var(--text-lg);
}

h6 {
  font-size: var(--text-md);
}

/* Paragraphs */
p {
  margin-bottom: 1rem;
}

/* Links */
a {
  color: var(--primary);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--primary-dark);
}

/* Article Content */
.article-content {
  font-family: var(--font-content);
  font-size: var(--text-md);
  line-height: var(--leading-relaxed);
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
  font-family: var(--font-heading);
  margin-top: 2rem;
  margin-bottom: 1rem;
}

/* Responsive Typography */
@media (max-width: 992px) {
  h1 {
    font-size: calc(var(--text-4xl) * 0.9);
  }
  
  h2 {
    font-size: calc(var(--text-3xl) * 0.9);
  }
  
  h3 {
    font-size: calc(var(--text-2xl) * 0.9);
  }
}

@media (max-width: 768px) {
  h1 {
    font-size: calc(var(--text-4xl) * 0.8);
  }
  
  h2 {
    font-size: calc(var(--text-3xl) * 0.8);
  }
  
  h3 {
    font-size: calc(var(--text-2xl) * 0.8);
  }
  
  body {
    font-size: calc(var(--text-base) * 0.95);
  }
}

@media (max-width: 576px) {
  h1 {
    font-size: calc(var(--text-4xl) * 0.7);
  }
  
  h2 {
    font-size: calc(var(--text-3xl) * 0.7);
  }
  
  h3 {
    font-size: calc(var(--text-2xl) * 0.7);
  }
  
  .article-content {
    font-size: var(--text-base);
  }
}
