import { useNavigate } from "react-router";


export default function ProfilePostContainer({userPosts}) {
    const navigate = useNavigate();
    const handleClick = () => {
        navigate(`/articles/articleDetails/${userPosts.id}`);
    };
    return(
        <div className='posts-container' onClick={handleClick}>
            <div className='posts-info'>

                <div className='user-data'>
                    <img src={userPosts.userImage} alt='img'/>
                    <div style={{textTransform:'capitalize'}}>{userPosts.userName}</div>
                </div>

                <div className='list-name'>
                    
                    {userPosts.title}
                </div>

                <div className='options'>
                    <div className='posts-number'>No Stories <i className="fa-solid fa-lock"></i> </div>
                    <i className="fa-solid fa-ellipsis" style={{fontSize: '18px'}}></i>
                </div>


            </div>
            <div className='posts-imgs'>

                <div className='img-container' style={{width:'100%', height: '100%', maxHeight: '100%'}}>
                    <img src={userPosts.imageUrl} alt='' style={{width: '100%', height: '100%', maxHeight:'100%'}}/>
                </div>
            </div>
        </div>
    );
}