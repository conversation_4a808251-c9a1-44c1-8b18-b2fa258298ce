/* Modern Articles Page Styles */

/* Fix for header transparency */
.articles-page-active .modern-header {
  background-color: rgba(20, 83, 45, 0.95);
  box-shadow: 0 4px 20px rgba(20, 83, 45, 0.2);
  z-index: 1000;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.articles-page-active .modern-header .logo-text,
.articles-page-active .modern-header .nav-links li a,
.articles-page-active .modern-header .search-button {
  color: var(--text-light);
}

.articles-page-active .modern-header .nav-links li a:hover {
  color: white;
}

.articles-page-active .modern-header .search-input-wrapper {
  background-color: rgba(255, 255, 255, 0.15);
}

.articles-page-active .modern-header .search-input {
  color: var(--text-light);
}

.articles-page-active .modern-header .search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

/* Scrolled header on articles page */
.articles-page-active .modern-header.scrolled {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(20, 83, 45, 0.1);
  box-shadow: 0 4px 20px rgba(20, 83, 45, 0.1);
}

.articles-page-active .modern-header.scrolled .logo-text,
.articles-page-active .modern-header.scrolled .nav-links li a,
.articles-page-active .modern-header.scrolled .search-button {
  color: var(--text-dark);
}

.articles-page-active .modern-header.scrolled .search-input-wrapper {
  background-color: var(--background-alt);
}

.articles-page-active .modern-header.scrolled .search-input {
  color: var(--text-dark);
}

.articles-page-active .modern-header.scrolled .search-input::placeholder {
  color: rgba(44, 62, 80, 0.7);
}

.articles-page-active .modern-header.scrolled .nav-links li a:hover {
  color: var(--primary);
}

/* Articles Page Layout */
.articles-page {
  padding-top: 80px;
  background-color: #f8fafc;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%2314532d' fill-opacity='0.03'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  min-height: 100vh;
  font-family: var(--font-primary);
  position: relative;
}

.articles-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(248, 250, 252, 0.8), rgba(248, 250, 252, 0.95));
  z-index: 0;
}

.articles-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 30px;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 30px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 1;
  border: 1px solid rgba(229, 231, 235, 0.8);
  overflow: hidden;
  margin-top: 20px;
  margin-bottom: 40px;
  transition: all 0.3s ease;
}

.articles-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 8px;
  background: linear-gradient(to right, #14532d, #059669);
  border-radius: 30px 30px 0 0;
}

/* Articles Hero Section */
.articles-hero {
  position: relative;
  height: 400px;
  background: linear-gradient(to right, rgba(20, 83, 45, 0.9), rgba(20, 83, 45, 0.7)),
              url('https://images.unsplash.com/photo-1523741543316-beb7fc7023d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80');
  background-size: cover;
  background-position: center;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  margin-bottom: 60px;
}

.articles-hero-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  width: 80%;
  max-width: 800px;
  z-index: 2;
}

.articles-hero-content h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.articles-hero-content p {
  font-size: 1.2rem;
  line-height: 1.6;
  margin: 0 auto;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

.articles-hero-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 30%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.4), transparent);
}

/* Breadcrumb Navigation */
.articles-breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  font-size: 0.9rem;
}

.articles-breadcrumb a {
  color: #64748b;
  text-decoration: none;
  transition: all 0.3s ease;
}

.articles-breadcrumb a:hover {
  color: #14532d;
}

.articles-breadcrumb .separator {
  margin: 0 10px;
  color: #94a3b8;
}

.articles-breadcrumb .current {
  color: #14532d;
  font-weight: 600;
}

/* Search and Filter Section */
.articles-search-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  background-color: white;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.articles-search-input {
  flex: 1;
  position: relative;
  max-width: 500px;
}

.articles-search-input input {
  width: 100%;
  padding: 12px 20px;
  padding-left: 45px;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  font-size: 1rem;
  color: #1e293b;
  transition: all 0.3s ease;
}

.articles-search-input input:focus {
  outline: none;
  border-color: #14532d;
  box-shadow: 0 0 0 3px rgba(20, 83, 45, 0.1);
}

.articles-search-input i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
}

.articles-filter-buttons {
  display: flex;
  gap: 15px;
}

.articles-filter-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #f1f5f9;
  border: none;
  border-radius: 10px;
  font-size: 0.9rem;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
}

.articles-filter-button:hover {
  background-color: #e2e8f0;
  color: #14532d;
}

.articles-filter-button.active {
  background-color: #14532d;
  color: white;
}

/* Articles Categories Section */
.articles-categories-section {
  margin-bottom: 60px;
}

.section-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-header h2 {
  font-size: 2.2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: #14532d;
  border-radius: 2px;
}

.section-header p {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 700px;
  margin: 0 auto;
}

.articles-categories-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.category-card {
  position: relative;
  height: 280px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.category-card-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  transition: all 0.4s ease;
}

.category-card:hover .category-card-bg {
  transform: scale(1.1);
}

.category-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.7));
  transition: all 0.4s ease;
}

.category-card:hover .category-card-overlay {
  background: linear-gradient(to bottom, rgba(20, 83, 45, 0.4), rgba(20, 83, 45, 0.8));
}

.category-card-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 25px;
  color: white;
  z-index: 2;
  transition: all 0.4s ease;
}

.category-card:hover .category-card-content {
  transform: translateY(-10px);
}

.category-card-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  transition: all 0.4s ease;
}

.category-card:hover .category-card-icon {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.category-card-icon i {
  font-size: 24px;
  color: white;
}

.category-card-title {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.category-card-description {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.category-card-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  opacity: 0.9;
  transition: all 0.3s ease;
}

.category-card:hover .category-card-link {
  opacity: 1;
}

.category-card-link i {
  transition: all 0.3s ease;
}

.category-card:hover .category-card-link i {
  transform: translateX(5px);
}

/* Featured Articles Section */
.featured-articles-section {
  margin-bottom: 60px;
}

.featured-articles-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.featured-article-card {
  display: flex;
  background-color: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.featured-article-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.featured-article-image {
  width: 40%;
  overflow: hidden;
}

.featured-article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.featured-article-card:hover .featured-article-image img {
  transform: scale(1.05);
}

.featured-article-content {
  width: 60%;
  padding: 25px;
  display: flex;
  flex-direction: column;
}

.featured-article-category {
  display: inline-block;
  padding: 5px 12px;
  background-color: rgba(20, 83, 45, 0.1);
  color: #14532d;
  font-size: 0.8rem;
  font-weight: 600;
  border-radius: 20px;
  margin-bottom: 15px;
}

.featured-article-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 10px;
  line-height: 1.4;
  transition: all 0.3s ease;
}

.featured-article-card:hover .featured-article-title {
  color: #14532d;
}

.featured-article-excerpt {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex-grow: 1;
}

.featured-article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: #94a3b8;
}

.featured-article-author {
  display: flex;
  align-items: center;
  gap: 8px;
}

.featured-article-author img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
}

.featured-article-date {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Newsletter Section */
.newsletter-section {
  background: linear-gradient(to right, #14532d, #166534);
  padding: 60px 40px;
  border-radius: 20px;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
  margin-bottom: 60px;
}

.newsletter-bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,.1)' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.3;
}

.newsletter-content {
  position: relative;
  z-index: 1;
  max-width: 600px;
  margin: 0 auto;
}

.newsletter-content h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 15px;
}

.newsletter-content p {
  font-size: 1.1rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.newsletter-form {
  display: flex;
  max-width: 500px;
  margin: 0 auto;
}

.newsletter-form input {
  flex: 1;
  padding: 15px 20px;
  border: none;
  border-radius: 10px 0 0 10px;
  font-size: 1rem;
  color: #1e293b;
}

.newsletter-form input:focus {
  outline: none;
}

.newsletter-form button {
  padding: 15px 25px;
  background-color: #14532d;
  border: none;
  border-radius: 0 10px 10px 0;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.newsletter-form button:hover {
  background-color: #0f3d22;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .articles-container {
    padding: 35px 25px;
    border-radius: 25px;
    max-width: 95%;
  }

  .articles-hero {
    height: 350px;
  }

  .articles-hero-content h1 {
    font-size: 2.5rem;
  }

  .articles-hero-content p {
    font-size: 1.1rem;
  }
}

@media (max-width: 992px) {
  .articles-categories-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .featured-articles-grid {
    grid-template-columns: 1fr;
  }

  .articles-hero {
    height: 300px;
  }

  .articles-hero-content h1 {
    font-size: 2.2rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .articles-container {
    padding: 30px 20px;
    border-radius: 20px;
    box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.1);
  }

  .articles-search-section {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }

  .articles-search-input {
    max-width: 100%;
  }

  .articles-filter-buttons {
    justify-content: space-between;
  }

  .articles-hero {
    height: 250px;
  }

  .articles-hero-content h1 {
    font-size: 1.8rem;
  }

  .articles-hero-content p {
    font-size: 1rem;
  }

  .featured-article-card {
    flex-direction: column;
  }

  .featured-article-image,
  .featured-article-content {
    width: 100%;
  }

  .featured-article-image {
    height: 200px;
  }

  .newsletter-form {
    flex-direction: column;
    gap: 10px;
  }

  .newsletter-form input,
  .newsletter-form button {
    width: 100%;
    border-radius: 10px;
  }
}

@media (max-width: 576px) {
  .articles-container {
    padding: 25px 15px;
    border-radius: 15px;
    box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.1);
    margin-top: 15px;
  }

  .articles-categories-grid {
    grid-template-columns: 1fr;
  }

  .articles-hero {
    height: 220px;
    margin-bottom: 40px;
  }

  .articles-hero-content h1 {
    font-size: 1.6rem;
  }

  .articles-hero-content p {
    font-size: 0.9rem;
  }

  .section-header h2 {
    font-size: 1.8rem;
  }

  .section-header p {
    font-size: 1rem;
  }

  .category-card {
    height: 250px;
  }
}
