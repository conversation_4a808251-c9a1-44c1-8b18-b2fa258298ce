import img from '../../assets/images/teemu-paananen-OOE4xAnBhKo-unsplash <EMAIL>';
import logoImg from '../../assets/images/whiteLeaf.png'
import { useState } from 'react';
import { useNavigate } from 'react-router';
import Swal from 'sweetalert2';
// import { reg } from './regFunc';

export default function Register() {
    const BASE_URL = process.env.REACT_APP_BASE_URL;

    const navigate = useNavigate();

    const [userInfo, SetUserInfo] = useState({
        name: '',
        email: '',
        password: '',
        c_password: '',
    })

    const handleChange = (e) => {
        SetUserInfo({ ...userInfo, [e.target.name]: e.target.value });
    };

    const handleRegisterBtn = (e) => {

        e.preventDefault();

        // console.log(userInfo.name, userInfo.email, userInfo.password, userInfo.cpassword);

        if (!userInfo.name || !userInfo.email || !userInfo.password || !userInfo.c_password) {
            Swal.fire({
                icon: 'warning',
                title: 'Missing Fields',
                text: 'Please fill in all the fields!',
                confirmButtonText: 'OK'
            });
            return;
        }




        fetch(`${BASE_URL}auth/register`, {
            method: "POST",
            headers: {
            "Content-Type": "application/json",
            },
            body: JSON.stringify({
                name: userInfo.name,
                email: userInfo.email,
                password: userInfo.password,
                c_password: userInfo.c_password,
            }),
        })
            .then(response => response.json())
            .then(data =>{
                console.log("Success:", data)
                console.log(data.message)
                console.log(data.status)
                if (data.status >= 200 && data.status < 300) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Registered Successfully!',
                        text: 'Your account has been created.',
                        confirmButtonText: 'Continue'
                      }).then((result) => {
                        if (result.isConfirmed) {
                          navigate("/login");
                        }
                      });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Registration Failed',
                            text: 'Something went wrong while creating your account. Please try again.',
                            confirmButtonText: 'OK'
                        });
                    // return data;
                    }
                // alert(data.message)
            })
            .catch(error => {
                console.error("Error:", error)
                Swal.fire({
                    icon: 'error',
                    title: 'Registration Failed',
                    text: 'Something went wrong while creating your account. Please try again.',
                    confirmButtonText: 'OK'
                });
            });
        }





    return (
        <div style={{backgroundColor: 'white', width: '100%'}}>
            <div className="register-page container">
                <img src={logoImg} alt='logo' className='register-logo'/>

                <div className='center-flex' style={{height: '100%', flexBasis: '50%'}}>
                    <img src={img} alt="logo" style={{height: '100%'}}/>
                </div>
                <div className='center-flex' style={{height: '100%', flexBasis: '50%'}}>
                    <form className="register-form" style={{textAlign: 'center'}}>
                        <h1>Create your account </h1>
                        <p>Welcome to our community</p>

                        <div className='between-flex' style={{flexDirection: 'column', gap: '20px'}}>

                            <div className='center-flex'>
                                <input placeholder='Name' name = 'name' onChange={handleChange} value={userInfo.name} required/>
                                <div>
                                    <i class="fa-solid fa-user"></i>
                                </div>
                            </div>

                            <div className='center-flex'>
                                <input type='email' placeholder='Email' name = 'email' onChange={handleChange} value={userInfo.email} required/>
                                <div>
                                    <i class="fa-solid fa-envelope"></i>
                                </div>
                            </div>

                            <div className='center-flex'>
                                <input type='Password' placeholder='password' name = 'password' onChange={handleChange} value={userInfo.password} required/>
                                <div>
                                    <i class="fa-solid fa-eye"></i>
                                </div>
                            </div>

                            <div className='center-flex'>
                                <input type='Password' placeholder='c_password' name = 'c_password' onChange={handleChange} value={userInfo.c_password} required style={{marginBottom: '0'}}/>
                                <div>
                                    <i class="fa-solid fa-eye"></i>
                                </div>
                            </div>

                        </div>

                        <button className='reg-btn' onClick={handleRegisterBtn}>Register</button>
                        <p className='options-head'>Or sign up with</p>
                        <div className='sign-with-acc'>

                            <div className='svg-container'>
                                <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="100" height="100" viewBox="0 0 48 48">
                                    <path fill="#039be5" d="M24 5A19 19 0 1 0 24 43A19 19 0 1 0 24 5Z"></path><path fill="#fff" d="M26.572,29.036h4.917l0.772-4.995h-5.69v-2.73c0-2.075,0.678-3.915,2.619-3.915h3.119v-4.359c-0.548-0.074-1.707-0.236-3.897-0.236c-4.573,0-7.254,2.415-7.254,7.917v3.323h-4.701v4.995h4.701v13.729C22.089,42.905,23.032,43,24,43c0.875,0,1.729-0.08,2.572-0.194V29.036z"></path>
                                </svg>
                            </div>



                            <div className='svg-container'>
                                <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="100" height="100" viewBox="0 0 48 48">
                                    <path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"></path><path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"></path><path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"></path><path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"></path>
                                </svg>
                            </div>


                        </div>
                        <p
                            style={{fontSize: '14px'}}>
                            Already have account ?

                                <span style={{color: '#015327', cursor: 'pointer',marginLeft:'5px'}}
                                    onClick={() => {
                                        navigate('/login')
                                }}>
                                Sign in
                            </span>
                        </p>
                    </form>
                </div>
            </div>
        </div>
    );
}
















// with Axios and app/json







// with Axios but with Search URL

// import axios from "axios";
// import qs from "qs";  // مكتبة لتحويل الكائن إلى x-www-form-urlencoded

// axios.post(`${BASE_URL}/auth/register`, qs.stringify({
//   name: "aUser",
//   email: "<EMAIL>",
//   password: "********",
//   c_password: "********",
// }), {
//   headers: {
//     "Content-Type": "application/x-www-form-urlencoded",
//   },
// })
//   .then(response => console.log("Success:", response.data))
//   .catch(error => console.error("Error:", error));





// fetch with app/json
