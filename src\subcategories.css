/* SubCategories Page Styles */

/* Fix for header transparency */
.subcategories-page-active .modern-header {
  background-color: rgba(20, 83, 45, 0.95);
  box-shadow: 0 4px 20px rgba(20, 83, 45, 0.2);
  z-index: 1000;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.subcategories-page-active .modern-header .logo-text,
.subcategories-page-active .modern-header .nav-links li a,
.subcategories-page-active .modern-header .modern-search .search-button {
  color: var(--text-light);
}

.subcategories-page-active .modern-header .modern-search .search-button {
  background: transparent;
  border: none;
  padding: 10px 15px;
  transition: var(--transition);
}

.subcategories-page-active .modern-header .nav-links li a:hover {
  color: white;
}

.subcategories-page-active .modern-header .modern-search .search-input-wrapper {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 30px;
  overflow: hidden;
  transition: var(--transition);
}

.subcategories-page-active .modern-header .modern-search .search-input {
  width: 200px;
  padding: 10px 15px;
  border: none;
  background: transparent;
  color: var(--text-light);
  font-size: 14px;
  transition: var(--transition);
}

.subcategories-page-active .modern-header .modern-search .search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

/* Scrolled header on subcategories page */
.subcategories-page-active .modern-header.scrolled {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(20, 83, 45, 0.1);
  box-shadow: 0 4px 20px rgba(20, 83, 45, 0.1);
}

.subcategories-page-active .modern-header.scrolled .logo-text,
.subcategories-page-active .modern-header.scrolled .nav-links li a,
.subcategories-page-active .modern-header.scrolled .modern-search .search-button {
  color: var(--text-dark);
}

.subcategories-page-active .modern-header.scrolled .modern-search .search-input-wrapper {
  background-color: var(--background-alt);
}

.subcategories-page-active .modern-header.scrolled .modern-search .search-input {
  color: var(--text-dark);
}

.subcategories-page-active .modern-header.scrolled .modern-search .search-input::placeholder {
  color: rgba(44, 62, 80, 0.7);
}

.subcategories-page-active .modern-header .modern-search.focused .search-input-wrapper {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.subcategories-page-active .modern-header.scrolled .modern-search.focused .search-input-wrapper {
  box-shadow: 0 0 0 2px var(--primary);
  background-color: rgba(5, 150, 105, 0.05);
}

.subcategories-page-active .modern-header.scrolled .nav-links li a:hover {
  color: var(--primary);
}

/* SubCategories Page Layout */
.subcategories-page {
  padding-top: 80px;
  background-color: #f8fafc;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%2314532d' fill-opacity='0.03'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  min-height: 100vh;
  font-family: var(--font-primary);
  position: relative;
}

.subcategories-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(248, 250, 252, 0.8), rgba(248, 250, 252, 0.95));
  z-index: 0;
}

/* Breadcrumb Navigation */
.subcategories-breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  font-size: 0.9rem;
  position: relative;
  z-index: 1;
}

.subcategories-breadcrumb a {
  color: #64748b;
  text-decoration: none;
  transition: all 0.3s ease;
}

.subcategories-breadcrumb a:hover {
  color: #14532d;
}

.subcategories-breadcrumb .separator {
  margin: 0 10px;
  color: #94a3b8;
}

.subcategories-breadcrumb .current {
  color: #14532d;
  font-weight: 600;
}

/* Main Container */
.subcategories-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 30px;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 30px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 1;
  border: 1px solid rgba(229, 231, 235, 0.8);
  overflow: hidden;
  margin-top: 20px;
  margin-bottom: 40px;
  transition: all 0.3s ease;
}

.subcategories-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 8px;
  background: linear-gradient(to right, #14532d, #059669);
  border-radius: 30px 30px 0 0;
}

/* Category Header */
.category-header {
  display: flex;
  background: linear-gradient(to right, rgba(20, 83, 45, 0.9), rgba(20, 83, 45, 0.7));
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
}

.category-info {
  flex: 1;
  padding: 30px;
  color: white;
}

.category-info h2 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.category-stats {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-stats li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 1.1rem;
}

.category-stats li i {
  margin-right: 10px;
  font-size: 1.2rem;
  opacity: 0.8;
}

.category-image {
  width: 40%;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-size: cover;
  background-position: center;
}

.category-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.category-image-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
}

.category-badge {
  display: inline-block;
  padding: 8px 16px;
  background: linear-gradient(45deg, #14532d, #059669);
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 20px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.category-image-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  border: 5px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  margin: 0 auto 20px;
}

.category-image-circle img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-title {
  font-size: 1.4rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Search Section */
.search-section {
  background-color: white;
  border-radius: 15px;
  padding: 25px 30px;
  margin-bottom: 40px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-section h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  position: relative;
  padding-bottom: 10px;
}

.search-section h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: #14532d;
  border-radius: 2px;
}

.search-section .search-input-wrapper {
  flex: 1;
  max-width: 500px;
  position: relative;
}

.search-section .search-input-wrapper input,
.search-section .search-input-wrapper .subcategory-search-input {
  width: 100%;
  padding: 12px 20px;
  padding-left: 45px;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  font-size: 1rem;
  color: #1e293b;
  transition: all 0.3s ease;
}

.search-section .search-input-wrapper input:focus,
.search-section .search-input-wrapper .subcategory-search-input:focus {
  outline: none;
  border-color: #14532d;
  box-shadow: 0 0 0 3px rgba(20, 83, 45, 0.1);
}

.search-section .search-input-wrapper i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
}

/* Subcategories Grid */
.subcategories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

/* Subcategory Card */
.subcategory-card {
  background-color: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.subcategory-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.subcategory-header {
  height: 180px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(to right, rgba(20, 83, 45, 0.8), rgba(20, 83, 45, 0.6));
}

.subcategory-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"%3E%3Cg fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Cpath opacity=".5" d="M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z"%2F%3E%3Cpath d="M6 5V0H5v5H0v1h5v94h1V6h94V5H6z"%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E');
  background-size: 100px 100px;
  opacity: 0.4;
  z-index: 0;
  transition: all 0.5s ease;
}

.subcategory-card:hover .subcategory-header::before {
  transform: scale(1.1);
}

.subcategory-image {
  position: absolute;
  width: 110px;
  height: 110px;
  border-radius: 50%;
  overflow: hidden;
  border: 5px solid #14532d;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  top: 50%;
  left: 30px;
  transform: translateY(-50%);
  z-index: 1;
  transition: all 0.3s ease;
}

.subcategory-card:hover .subcategory-image {
  transform: translateY(-50%) scale(1.05);
  border-color: #059669;
}

.subcategory-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s ease;
}

.subcategory-card:hover .subcategory-image img {
  transform: scale(1.1);
}

.subcategory-content {
  padding: 25px;
}

.subcategory-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #14532d;
  margin: 0 0 15px 0;
  transition: all 0.3s ease;
}

.subcategory-card:hover .subcategory-title {
  color: #059669;
}

.subcategory-description {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #64748b;
  margin: 0;
  height: 80px;
  overflow: hidden;
}

.subcategory-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 25px;
  border-top: 1px solid #f1f5f9;
}

.subcategory-views {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

.subcategory-views i {
  color: #94a3b8;
}

.read-more-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #14532d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.read-more-button:hover {
  background-color: #059669;
  transform: translateY(-2px);
}

.read-more-button i {
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.read-more-button:hover i {
  transform: translateX(3px);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .subcategories-container {
    padding: 35px 25px;
    border-radius: 25px;
    max-width: 95%;
  }

  .category-info h2 {
    font-size: 2rem;
  }

  .category-image-circle {
    width: 130px;
    height: 130px;
  }
}

@media (max-width: 992px) {
  .category-header {
    flex-direction: column;
  }

  .category-info {
    padding: 25px;
  }

  .category-image {
    width: 100%;
    padding: 30px;
  }

  .subcategories-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .subcategories-container {
    padding: 30px 20px;
    border-radius: 20px;
    box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.1);
  }

  .search-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .search-input-wrapper {
    max-width: 100%;
  }

  .category-info h2 {
    font-size: 1.8rem;
  }

  .category-stats li {
    font-size: 1rem;
  }

  .subcategory-image {
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .subcategory-card:hover .subcategory-image {
    transform: translate(-50%, -50%) scale(1.05);
  }
}

@media (max-width: 576px) {
  .subcategories-container {
    padding: 25px 15px;
    border-radius: 15px;
    box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.1);
    margin-top: 15px;
  }

  .category-info h2 {
    font-size: 1.6rem;
  }

  .category-image-circle {
    width: 110px;
    height: 110px;
  }

  .subcategories-grid {
    grid-template-columns: 1fr;
  }

  .subcategory-header {
    height: 160px;
  }

  .subcategory-image {
    width: 100px;
    height: 100px;
  }

  .subcategory-title {
    font-size: 1.2rem;
  }

  .subcategory-description {
    font-size: 0.9rem;
  }
}
