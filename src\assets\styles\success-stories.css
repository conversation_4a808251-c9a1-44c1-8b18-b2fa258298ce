/* Success Stories Section Styles */

.success-stories-section {
  padding: 80px 0;
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;
}

.success-stories-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23059669" fill-opacity="0.05" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,250.7C960,235,1056,181,1152,165.3C1248,149,1344,171,1392,181.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-size: cover;
  background-position: center bottom;
  opacity: 0.5;
  z-index: 0;
}

.success-stories-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.success-stories-header {
  text-align: center;
  margin-bottom: 50px;
}

.success-stories-header h2 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-dark);
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

.success-stories-header h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--primary);
  border-radius: 2px;
}

.success-stories-header p {
  font-size: var(--text-md);
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: var(--leading-relaxed);
}

.success-stories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
}

.success-story-card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.success-story-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.story-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.story-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.success-story-card:hover .story-image {
  transform: scale(1.05);
}

.story-achievement {
  position: absolute;
  bottom: 15px;
  right: 15px;
  background-color: var(--primary);
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 4px 10px rgba(5, 150, 105, 0.3);
}

.story-content {
  padding: 25px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.story-quote {
  position: relative;
  margin-bottom: 20px;
}

.quote-icon {
  color: var(--primary);
  font-size: 1.5rem;
  opacity: 0.3;
  position: absolute;
  top: -10px;
  left: -5px;
}

.story-quote p {
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  color: #555;
  font-style: italic;
  padding-left: 20px;
  font-family: var(--font-content);
}

.story-author {
  margin-top: auto;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.story-author h4 {
  font-size: var(--text-md);
  font-weight: var(--font-semibold);
  color: var(--text-dark);
  margin: 0 0 5px 0;
}

.story-author p {
  font-size: var(--text-xs);
  color: #777;
  margin: 0;
}

.success-stories-cta {
  text-align: center;
  margin-top: 30px;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .success-stories-section {
    padding: 60px 0;
  }

  .success-stories-header h2 {
    font-size: calc(var(--text-3xl) * 0.9);
  }
}

@media (max-width: 768px) {
  .success-stories-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 576px) {
  .success-stories-section {
    padding: 40px 0;
  }

  .success-stories-header h2 {
    font-size: calc(var(--text-3xl) * 0.7);
  }

  .success-stories-header p {
    font-size: var(--text-base);
  }

  .story-image-container {
    height: 180px;
  }

  .story-quote p {
    font-size: var(--text-xs);
  }
}
