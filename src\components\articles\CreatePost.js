// import Header from './Header';
// import Footer from './Footer';
// import { useState, useRef, useEffect, useMemo } from 'react';
// import Swal from 'sweetalert2';
// import '../create-post.css';


// export default function CreatePost() {

//     const BASE_URL = process.env.REACT_APP_BASE_URL;
//     // console.log(BASE_URL);


//     // localStorage.clear();
//     // localStorage.setItem("token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vYnJpbGxpYW50LWV4aGliaXRpb24tY29hbGl0aW9uLWlvd2EudHJ5Y2xvdWRmbGFyZS5jb20vYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDMxOTkyMTksImV4cCI6MTc0MzIwMjgxOSwibmJmIjoxNzQzMTk5MjE5LCJqdGkiOiJiS1E5eXdXakxWcWt0M3NCIiwic3ViIjoiMTAiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.g2hZ10rJubQD7j1q7rhqIW1sa3LpQo9jwYSD_PxCH6c")

//     const token = localStorage.getItem("token");
//     // console.log(token)

//     const [fileName, setFileName] = useState('No file chosen');
//     const [mainTitle, setMainTitle] = useState('');
//     const [selectedCategory, setSelectedCategory] = useState([]);
//     const [selectedSubcategory, setSelectedSubcategory] = useState([]);
//     const [sections, setSections] = useState([
//         {
//             id: "section-1",
//             title: "",
//             content: ""
//         },
//     ]);

//     const [sectionCount, setSectionCount] = useState(2);
//     const imageInputRef = useRef(null);

//     useEffect(() => {
    
//         fetch(`${BASE_URL}categories`, {
//             headers: {
//                 'Content-Type': 'application/json',
//                 'Accept': 'application/json',
//                 'ngrok-skip-browser-warning':  '69420'
//             }   
//         })
//         .then((response) => response.json())
//         .then((data) => {
//             // console.log(data);
//             setSelectedCategory(data.data.categories); 
//         }) 

//     }, []);

//     // Get available subcategories based on selected category
//     const getSubcategories = (categoryId) => {

//         fetch(`${BASE_URL}sub-categories/get-by-categorie-id?categorieId=${categoryId}`, {
//             headers: {
//                 'Content-Type': 'application/json',
//                 'Accept': 'application/json',
//                 'ngrok-skip-browser-warning':  '69420'
//             }   
//         })
//         .then(response => response.json())
//         .then(data => {
//             console.log(data);
//             setSelectedSubcategory(data.data.subCategories);
//             // return data.data.subCategories;
//         })
//         .catch( (error) => {
//             console.error("Error fetching subcategories:", error);
//         })
//     };

//     // Handle category change
//     const handleCategoryChange = (e) => {
//         console.log("Selected category:", e.target.value);
//         // console.log("Selected category:", e.target);

//         const newCategory = e.target.value;
//         setSelectedCategory(newCategory);
//         setSelectedSubcategory(''); // Reset subcategory when category changes
//         getSubcategories(newCategory);
//     };

//     // Add a class to the body when the component mounts
//     useEffect(() => {
//         document.body.classList.add('create-post-page-active');

//         // Clean up function to remove the class when component unmounts
//         return () => {
//             document.body.classList.remove('create-post-page-active');
//         };
//     }, []);

//     // Add a new section after the specified section
//     const addSectionAfter = (afterId) => {
//         const newSection = {
//             id: `section-${sectionCount}`,
//             title: "",
//             content: ""
//         };

//         const index = sections.findIndex((sec) => sec.id === afterId);
//         const newSections = [...sections];
//         newSections.splice(index + 1, 0, newSection); // Insert after the specified section

//         setSections(newSections);
//         setSectionCount(sectionCount + 1);
//     };


//     const updateSection = (id, field, value) => {

//         setSections( sections.map(
//             (section) => section.id === id ? { ...section, [field]: value } : section)
//         );

//     };

//       // حذف قسم
//     const deleteSection = (id) => {
//         if (sections.length === 1) {
//             Swal.fire({
//                 icon: "info",
//                 title: "Can't Delete",
//                 text: "At least one section is required for your article.",
//                 confirmButtonText: "Understood",
//                 confirmButtonColor: '#14532d'
//               });
//             // alert("لا يمكن حذف القسم الوحيد المتبقي.");
//             return;
//         }
//         setSections(sections.filter(
//             (section) => section.id !== id)
//         );

//     };

//     const handlePostBtn = (e) => {

//         e.preventDefault();

//         // Validate category and subcategory selection
//         if (!selectedCategory) {
//             Swal.fire({
//                 icon: "warning",
//                 title: "Category Required",
//                 text: "Please select a category for your article.",
//                 confirmButtonText: "Got it!",
//                 confirmButtonColor: '#14532d'
//             });
//             return;
//         }

//         if (!selectedSubcategory) {
//             Swal.fire({
//                 icon: "warning",
//                 title: "Subcategory Required",
//                 text: "Please select a subcategory for your article.",
//                 confirmButtonText: "Got it!",
//                 confirmButtonColor: '#14532d'
//             });
//             return;
//         }

//         // Validate that each section has a title and content
//         const isValid = sections.every(
//             (sec) => sec.title.trim() !== "" && sec.content.trim() !== ""
//         );

//         if (!isValid) {
//             Swal.fire({
//                 icon: "warning",
//                 title: "Empty Fields",
//                 text: "Please fill in all section titles and contents before publishing.",
//                 confirmButtonText: "Got it!",
//                 confirmButtonColor: '#14532d'
//               });
//             return;
//         }

//         const imageFile = imageInputRef.current.files[0];
//         // console.log(imageFile);

//         const formData = new FormData();
//         formData.append("main_title", mainTitle);
//         formData.append("category_id", selectedCategory);
//         formData.append("subcategory_id", selectedSubcategory);
//         formData.append("home_page", "1");
//         formData.append("most_famous", "0");
//         if (imageFile) {
//             // console.log(imageFile)
//             formData.append("image_file", imageFile);
//         }
//         formData.append("articlesubcategorie_id", "1"); // Legacy field, keeping for compatibility
//         sections.forEach((section) => {
//             formData.append("title[]", section.title);
//             formData.append("froala_content[]", section.content);
//         });

//         // formData.forEach((value, key) => {
//         //     console.log(`${key}:`, value);
//         // });

//         fetch(`${BASE_URL}article/pending/store`, {
//             method: "POST",
//             body: formData,
//             headers: {
//                 Authorization: `Bearer ${token}`
//             },
//         })
//         .then(response => response.json())
//         .then(data =>{
//             // console.log("Success:", data)
//             Swal.fire({
//                 icon: 'success',
//                 title: 'Article Submitted!',
//                 text: 'Your article has been submitted successfully and is now awaiting approval.',
//                 showClass: {
//                     popup: 'animate__animated animate__fadeInDown'
//                 },
//                 hideClass: {
//                     popup: 'animate__animated animate__fadeOutUp'
//                 },
//                 confirmButtonText: 'Awesome!',
//                 confirmButtonColor: '#14532d'
//             }).then(() => {
//                 // Reset all form fields after successful submission
//                 setMainTitle('');
//                 setSelectedCategory('');
//                 setSelectedSubcategory('');
//                 setSections([
//                     { id: "section-1", title: "", content: "" }
//                 ]);
//                 setSectionCount(2);
//                 if (imageInputRef.current) {
//                     imageInputRef.current.value = null;
//                 }
//                 setFileName('No file chosen');
//                 // Reset file name display
//                 const fileNameEl = document.getElementById("file-name");
//                 if (fileNameEl) {
//                     fileNameEl.textContent = "No file chosen";
//                 }
//             });
//         })
//         .catch(error => {
//             console.error("Error:", error);
//             Swal.fire({
//                 icon: "error",
//                 title: "Submission Failed",
//                 text: "Something went wrong while posting your article. Please try again.",
//                 confirmButtonText: "Okay",
//                 confirmButtonColor: '#14532d'
//             });
//         })
//     }

//     const handleFileChange = () => {
//         const file = imageInputRef.current.files[0];
//         setFileName(file ? file.name : 'No file chosen');
//     };

//     return (
//         <div className="modern-create-post">
//             <Header />
//             <div className="create-post-container">
//                 <div className="create-post-header">
//                     <h1>Create New Article</h1>
//                     <p>Share your farming knowledge and experiences with the community</p>
//                 </div>

//                 {/* Category Select Boxes */}
//                 <div className="category-selects-wrapper">
//                     <div className="category-select-container">
//                         <label className="category-select-label">Select Category</label>
//                         <select
//                             className="category-select"
//                             // multiple={true}
//                             value={selectedCategory}
//                             onChange={handleCategoryChange}
//                         >
//                             <option value="">-- Select a Category --</option>
//                             {selectedCategory.map(category => (
//                                 <option key={category.id} value={category.id}>
//                                     {category.name}
//                                 </option>
//                             ))}
//                         </select>
//                     </div>

//                     <div className="category-select-container">
//                         <label className="category-select-label">Select Subcategory</label>
//                         <select
//                             className="category-select"
//                             // multiple={true}
//                             value={selectedSubcategory}
//                             onChange={(e) => setSelectedSubcategory(e.target.value)}
//                             disabled={!selectedCategory}
//                         >
//                             <option value="">-- Select a Subcategory --</option>
//                             {selectedSubcategory.map(subcategory => (
//                                 <option key={subcategory.id} value={subcategory.id}>
//                                     {subcategory.name}
//                                 </option>
//                             ))}
//                         </select>
//                     </div>
//                 </div>

//                 <div className="main-title-wrapper">
//                     <input
//                         type="text"
//                         placeholder="Enter your article title"
//                         className="modern-main-title"
//                         value={mainTitle}
//                         onChange={(e) => setMainTitle(e.target.value)}
//                     />
//                 </div>

//                 <div className="modern-file-upload">
//                     <div className="file-upload-header">
//                         <h3>Featured Image</h3>
//                         <p>Upload a high-quality image to represent your article (recommended size: 1200×800px)</p>
//                     </div>
//                     <div className="file-upload-area">
//                         <div className="file-info">
//                             <div className="file-icon">
//                                 <i className="fa-solid fa-image"></i>
//                             </div>
//                             <div className="file-name" id="file-name">{fileName}</div>
//                         </div>
//                         <label htmlFor="post-image" className="modern-file-upload-btn">
//                             <i className="fa-solid fa-upload"></i>
//                             Choose File
//                         </label>
//                         <input type="file" id="post-image" ref={imageInputRef} onChange={handleFileChange} />
//                     </div>
//                 </div>

//                 <div className="post-sections">
//                     {sections.map((section, index) => (
//                         <div className="post-section" key={section.id}>
//                             <div className="section-header">
//                                 <div className="section-number">Section {index + 1}</div>
//                                 <div className="section-controls">
//                                     <button
//                                         className="section-control-btn"
//                                         onClick={() => addSectionAfter(section.id)}
//                                         title="Add section after this one"
//                                     >
//                                         <i className="fa-solid fa-plus"></i>
//                                     </button>
//                                     <button
//                                         className="section-control-btn delete"
//                                         onClick={() => deleteSection(section.id)}
//                                         title="Delete this section"
//                                     >
//                                         <i className="fa-solid fa-trash"></i>
//                                     </button>
//                                 </div>
//                             </div>
//                             <div className="section-content">
//                                 <input
//                                     type="text"
//                                     placeholder="Section Title"
//                                     className="section-title-input"
//                                     value={section.title}
//                                     onChange={(e) => updateSection(section.id, "title", e.target.value)}
//                                 />
//                                 <textarea
//                                     placeholder="Write your content here..."
//                                     className="section-content-textarea"
//                                     value={section.content}
//                                     onChange={(e) => updateSection(section.id, "content", e.target.value)}
//                                 ></textarea>
//                             </div>
//                         </div>
//                     ))}
//                 </div>

//                 <button
//                     className="add-section-btn"
//                     onClick={() => addSectionAfter(sections[sections.length - 1].id)}
//                 >
//                     <i className="fa-solid fa-plus"></i>
//                     Add New Section
//                 </button>

//                 <div className="publish-container">
//                     <button className="modern-publish-btn" onClick={handlePostBtn}>
//                         <i className="fa-solid fa-paper-plane"></i>
//                         Publish Article
//                     </button>
//                 </div>
//             </div>
//             {/* <Footer/> */}
//         </div>
//     );
// }




import Header from './Header';
import Footer from './Footer';
import { useState, useRef, useEffect, useMemo } from 'react';
import Swal from 'sweetalert2';
import '../create-post.css';


export default function CreatePost() {

    const BASE_URL = process.env.REACT_APP_BASE_URL;
    // console.log(BASE_URL);


    // localStorage.clear();
    // localStorage.setItem("token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vYnJpbGxpYW50LWV4aGliaXRpb24tY29hbGl0aW9uLWlvd2EudHJ5Y2xvdWRmbGFyZS5jb20vYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDMxOTkyMTksImV4cCI6MTc0MzIwMjgxOSwibmJmIjoxNzQzMTk5MjE5LCJqdGkiOiJiS1E5eXdXakxWcWt0M3NCIiwic3ViIjoiMTAiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.g2hZ10rJubQD7j1q7rhqIW1sa3LpQo9jwYSD_PxCH6c")

    const token = localStorage.getItem("token");
    // console.log(token)



    
    const [fileName, setFileName] = useState('No file chosen');
    const [mainTitle, setMainTitle] = useState('');

    // set selectedCategoryId to the id of the selected category 
    const [categories, setCategories] = useState([]);
    const [selectedCategory, setSelectedCategory] = useState([]);
    const [selectedCategoryId, setSelectedCategoryId] = useState('');

     // set selectedCategoryId to the id of the selected category
    const [subcategories, setSubcategories] = useState([]);
    const [selectedSubcategory, setSelectedSubcategory] = useState([]);
    const [selectedSubcategoryId, setSelectedSubcategoryId] = useState('');


    const [sections, setSections] = useState([
        {
            id: "section-1",
            title: "",
            content: ""
        },
    ]);

    const [sectionCount, setSectionCount] = useState(2);
    const imageInputRef = useRef(null);

    useEffect(() => {
    
        fetch(`${BASE_URL}categories`, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'ngrok-skip-browser-warning':  '69420'
            }   
        })
        .then((response) => response.json())
        .then((data) => {
            // console.log(data);
            // setSelectedCategory(data.data.categories); 
            setCategories(data.data.categories);
        }) 

    }, []);

    // Get available subcategories based on selected category
    const getSubcategories = (categoryId) => {

        fetch(`${BASE_URL}sub-categories/get-by-categorie-id?categorieId=${categoryId}`, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'ngrok-skip-browser-warning': '69420'
            }
        })
        .then(response => response.json())
        .then(data => {
            setSubcategories(data.data.subCategories);
        })
        .catch(error => {
            console.error("Error fetching subcategories:", error);
        });
    };

    // Handle category change
    const handleCategoryChange = (e) => {

const newCategoryId = e.target.value;
    setSelectedCategoryId(newCategoryId);
    setSelectedSubcategoryId(''); // reset subcategory

    if (newCategoryId) {
        fetch(`${BASE_URL}sub-categories/get-by-categorie-id?categorieId=${newCategoryId}`, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'ngrok-skip-browser-warning': '69420'
            }
        })
        .then(response => response.json())
        .then(data => {
            setSubcategories(data.data.subCategories);
        })
        .catch(error => {
            console.error("Error fetching subcategories:", error);
        });
    } else {
        setSubcategories([]);
    }

    };

    // Add a class to the body when the component mounts
    useEffect(() => {
        document.body.classList.add('create-post-page-active');

        // Clean up function to remove the class when component unmounts
        return () => {
            document.body.classList.remove('create-post-page-active');
        };
    }, []);

    // Add a new section after the specified section
    const addSectionAfter = (afterId) => {
        const newSection = {
            id: `section-${sectionCount}`,
            title: "",
            content: ""
        };

        const index = sections.findIndex((sec) => sec.id === afterId);
        const newSections = [...sections];
        newSections.splice(index + 1, 0, newSection); // Insert after the specified section

        setSections(newSections);
        setSectionCount(sectionCount + 1);
    };


    const updateSection = (id, field, value) => {

        setSections( sections.map(
            (section) => section.id === id ? { ...section, [field]: value } : section)
        );

    };

      // حذف قسم
    const deleteSection = (id) => {
        if (sections.length === 1) {
            Swal.fire({
                icon: "info",
                title: "Can't Delete",
                text: "At least one section is required for your article.",
                confirmButtonText: "Understood",
                confirmButtonColor: '#14532d'
              });
            // alert("لا يمكن حذف القسم الوحيد المتبقي.");
            return;
        }
        setSections(sections.filter(
            (section) => section.id !== id)
        );

    };

    const handlePostBtn = (e) => {

        e.preventDefault();

        // Validate category and subcategory selection
        if (!selectedCategory) {
            Swal.fire({
                icon: "warning",
                title: "Category Required",
                text: "Please select a category for your article.",
                confirmButtonText: "Got it!",
                confirmButtonColor: '#14532d'
            });
            return;
        }

        if (!selectedSubcategory) {
            Swal.fire({
                icon: "warning",
                title: "Subcategory Required",
                text: "Please select a subcategory for your article.",
                confirmButtonText: "Got it!",
                confirmButtonColor: '#14532d'
            });
            return;
        }

        // Validate that each section has a title and content
        const isValid = sections.every(
            (sec) => sec.title.trim() !== "" && sec.content.trim() !== ""
        );

        if (!isValid) {
            Swal.fire({
                icon: "warning",
                title: "Empty Fields",
                text: "Please fill in all section titles and contents before publishing.",
                confirmButtonText: "Got it!",
                confirmButtonColor: '#14532d'
              });
            return;
        }

        const imageFile = imageInputRef.current.files[0];
        // console.log(imageFile);

        const formData = new FormData();
        formData.append("main_title", mainTitle);

        formData.append("home_page", "1");
        formData.append("most_famous", "0");

        if (imageFile) {
            formData.append("image_file", imageFile);
        }
        formData.append("articlesubcategorie_id", selectedSubcategoryId); // Legacy field, keeping for compatibility
        sections.forEach((section) => {
            formData.append("title[]", section.title);
            formData.append("froala_content[]", section.content);
        });

        fetch(`${BASE_URL}article/pending/store`, {
            method: "POST",
            body: formData,
            headers: {
                Authorization: `Bearer ${token}`
            },
        })
        .then(response => response.json())
        .then(data => {
            console.log("Success:", data)
            Swal.fire({
                icon: 'success',
                title: 'Article Submitted!',
                text: 'Your article has been submitted successfully and is now awaiting approval.',
                showClass: {
                    popup: 'animate__animated animate__fadeInDown'
                },
                hideClass: {
                    popup: 'animate__animated animate__fadeOutUp'
                },
                confirmButtonText: 'Awesome!',
                confirmButtonColor: '#14532d'
            }).then(() => {

                // Reset all form fields after successful submission
                setMainTitle('');
                setSelectedCategory('');
                setSelectedSubcategory('');
                setCategories([]); // optional: clear categories if needed
                setSubcategories([]); // optional: clear subcategories
                setSections([
                    { id: "section-1", title: "", content: "" }
                ]);
                setSectionCount(2);
                if (imageInputRef.current) {
                    imageInputRef.current.value = null;
                }
                setFileName('No file chosen');
                // Reset file name display
                const fileNameEl = document.getElementById("file-name");
                if (fileNameEl) {
                    fileNameEl.textContent = "No file chosen";
                }
            });
        })
        .catch(error => {
            console.error("Error:", error);
            Swal.fire({
                icon: "error",
                title: "Submission Failed",
                text: "Something went wrong while posting your article. Please try again.",
                confirmButtonText: "Okay",
                confirmButtonColor: '#14532d'
            });
        })
    }

    const handleFileChange = () => {
        const file = imageInputRef.current.files[0];
        setFileName(file ? file.name : 'No file chosen');
    };

    return (
        <div className="modern-create-post">
            <Header />
            <div className="create-post-container">
                <div className="create-post-header">
                    <h1>Create New Article</h1>
                    <p>Share your farming knowledge and experiences with the community</p>
                </div>

                {/* Category Select Boxes */}
                <div className="category-selects-wrapper">
                    <div className="category-select-container">
                        <label className="category-select-label">Select Category</label>

                        <select
                            className="category-select"
                            value={selectedCategoryId}
                            onChange={handleCategoryChange}
                        >
                            <option value="">-- Select a Category --</option>
                            {categories.map(category => (
                                <option key={category.id} value={category.id}>
                                    {category.name}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div className="category-select-container">
                        <label className="category-select-label">Select Subcategory</label>
                        <select
                            className="category-select"
                            value={selectedSubcategoryId}
                            onChange={(e) => setSelectedSubcategoryId(e.target.value)}
                            disabled={!selectedCategoryId}
                        >
                            <option value="">-- Select a Subcategory --</option>
                            {subcategories.map(sub => (
                                <option key={sub.id} value={sub.id}>
                                    {sub.name}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>

                <div className="main-title-wrapper">
                    <input
                        type="text"
                        placeholder="Enter your article title"
                        className="modern-main-title"
                        value={mainTitle}
                        onChange={(e) => setMainTitle(e.target.value)}
                    />
                </div>

                <div className="modern-file-upload">
                    <div className="file-upload-header">
                        <h3>Featured Image</h3>
                        <p>Upload a high-quality image to represent your article (recommended size: 1200×800px)</p>
                    </div>
                    <div className="file-upload-area">
                        <div className="file-info">
                            <div className="file-icon">
                                <i className="fa-solid fa-image"></i>
                            </div>
                            <div className="file-name" id="file-name">{fileName}</div>
                        </div>
                        <label htmlFor="post-image" className="modern-file-upload-btn">
                            <i className="fa-solid fa-upload"></i>
                            Choose File
                        </label>
                        <input type="file" id="post-image" ref={imageInputRef} onChange={handleFileChange} />
                    </div>
                </div>

                <div className="post-sections">
                    {sections.map((section, index) => (
                        <div className="post-section" key={section.id}>
                            <div className="section-header">
                                <div className="section-number">Section {index + 1}</div>
                                <div className="section-controls">
                                    <button
                                        className="section-control-btn"
                                        onClick={() => addSectionAfter(section.id)}
                                        title="Add section after this one"
                                    >
                                        <i className="fa-solid fa-plus"></i>
                                    </button>
                                    <button
                                        className="section-control-btn delete"
                                        onClick={() => deleteSection(section.id)}
                                        title="Delete this section"
                                    >
                                        <i className="fa-solid fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div className="section-content">
                                <input
                                    type="text"
                                    placeholder="Section Title"
                                    className="section-title-input"
                                    value={section.title}
                                    onChange={(e) => updateSection(section.id, "title", e.target.value)}
                                />
                                <textarea
                                    placeholder="Write your content here..."
                                    className="section-content-textarea"
                                    value={section.content}
                                    onChange={(e) => updateSection(section.id, "content", e.target.value)}
                                ></textarea>
                            </div>
                        </div>
                    ))}
                </div>

                <button
                    className="add-section-btn"
                    onClick={() => addSectionAfter(sections[sections.length - 1].id)}
                >
                    <i className="fa-solid fa-plus"></i>
                    Add New Section
                </button>

                <div className="publish-container">
                    <button className="modern-publish-btn" onClick={handlePostBtn}>
                        <i className="fa-solid fa-paper-plane"></i>
                        Publish Article
                    </button>
                </div>
            </div>
        </div>
    );
}
