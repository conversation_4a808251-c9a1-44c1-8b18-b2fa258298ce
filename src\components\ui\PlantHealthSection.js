import React from 'react';
import '../../assets/styles/plant-health-section.css';

const PlantHealthSection = () => {
  return (
    <section className="plant-health-section">
      <div className="plant-health-container">
        <div className="plant-health-header">
          <h2>Everything You Need For <span className="highlight">Plant Health</span></h2>
          <p className="plant-health-subtitle">
            Our comprehensive platform provides all the tools and knowledge you need to
            identify, treat, and prevent plant diseases.
          </p>
        </div>

        <div className="plant-health-cards">
          <div className="plant-health-card">
            <div className="card-icon">
              <i className="fas fa-microscope"></i>
            </div>
            <h3>Disease Identification</h3>
            <p>Instantly identify common plant diseases with our advanced visual recognition system.</p>
          </div>

          <div className="plant-health-card">
            <div className="card-icon">
              <i className="fas fa-shield-alt"></i>
            </div>
            <h3>Treatment Solutions</h3>
            <p>Get expert-backed treatment recommendations for any plant health issue.</p>
          </div>

          <div className="plant-health-card">
            <div className="card-icon">
              <i className="fas fa-search"></i>
            </div>
            <h3>Symptom Search</h3>
            <p>Search by visible symptoms to narrow down potential diseases affecting your plants.</p>
          </div>

          <div className="plant-health-card">
            <div className="card-icon">
              <i className="fas fa-book-open"></i>
            </div>
            <h3>Expert Articles</h3>
            <p>Access our library of detailed articles written by agricultural specialists.</p>
          </div>

          <div className="plant-health-card">
            <div className="card-icon">
              <i className="fas fa-bolt"></i>
            </div>
            <h3>Quick Diagnosis</h3>
            <p>Get instant results and actionable advice for urgent plant health concerns.</p>
          </div>

          <div className="plant-health-card">
            <div className="card-icon">
              <i className="fas fa-seedling"></i>
            </div>
            <h3>Preventive Care</h3>
            <p>Learn how to keep your plants healthy and prevent common diseases.</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PlantHealthSection;
