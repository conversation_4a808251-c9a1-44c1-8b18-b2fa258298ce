* {
  box-sizing: border-box;
}

html, body {
  overflow-x: hidden;
  max-width: 100%;
}

body {
  font-family: "Poppins", sans-serif;
  margin: 0;
}

/* width */
::-webkit-scrollbar {
  width: 12px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #2e7d32;
  border-radius: 6px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #1b5e20;
}

.circle {
  border-radius: 50%;
}

.grad {
  background-image: linear-gradient(45deg, #2e7d32, #388e3c);
}

.container {
  padding-left: 15px;
  padding-right: 15px;
  margin-left: auto;
  margin-right: auto;
  overflow-x: hidden;
  max-width: 100%;
}

/* Small */
@media (min-width: 768px) {
  .container {
    width: 750px;
  }
}

/* Medium */
@media (min-width: 992px) {
  .container {
    width: 970px;
  }
}

/* Large */
@media (min-width: 1200px) {
  .container {
    width: 1170px;
  }
}

.between-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.center-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-style {
  font-weight: 600;
  font-size: 16px;
  list-style: none;
  margin: 0;
  padding: 0;
}

a {
  text-decoration: none;
  position: relative;
}

/* Start Header Style */

header {
  position: absolute;
  width: 100%;
  padding: 20px 0;
  z-index: 100;
  transition: all 0.3s ease;
}

header.grad {
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

header.scrolled {
  position: fixed;
  top: 0;
  left: 0;
  animation: slideDown 0.5s ease;
  backdrop-filter: blur(10px);
  background-color: rgba(46, 125, 50, 0.95);
}

@keyframes slideDown {
  from { transform: translateY(-100%); }
  to { transform: translateY(0); }
}

.logo-container {
  transition: all 0.3s ease;
}

.logo-container:hover {
  transform: scale(1.05);
}

.write-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #43a047;
  border-radius: 20px;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.write-button:hover {
  background-color: #2e7d32;
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
}

.write-button span {
  font-weight: 400;
  color: white;
}

.user-menu {
  position: relative;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.menu-toggle {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-toggle svg {
  width: 12px;
  height: 12px;
  transition: transform 0.3s ease;
}
/*
.auth-buttons {
  display: flex;
  gap: 15px;
}

.auth-btn {
  background: none;
  border: none;
  color: white;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.login-btn {
  background-color: rgba(255, 255, 255, 0.1);
}

.signup-btn {
  background-color: #43a047;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.login-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.signup-btn:hover {
  background-color: #2e7d32;
  transform: translateY(-2px);
} */

.drop-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  padding: 10px 0;
  min-width: 150px;
  margin-top: 10px;
  z-index: 100;
  animation: fadeIn 0.3s ease;
  transform-origin: top right;
}

.drop-menu::before {
  content: '';
  position: absolute;
  top: -5px;
  right: 10px;
  width: 10px;
  height: 10px;
  background-color: white;
  transform: rotate(45deg);
}

.drop-menu li {
  list-style: none;
}

.drop-menu li a {
  display: block;
  padding: 10px 20px;
  color: #333;
  transition: all 0.2s ease;
  font-size: 14px;
}

.drop-menu li a:hover {
  background-color: #f5f5f5;
  color: #2e7d32;
}

@media (max-width: 992px) {
  header {
    padding: 15px 0;
  }
}

.pages {
  display: flex;
  gap: 10px;
}

.pages a {
  color: white;
  padding: 8px 15px;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.pages a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.pages a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background-color: white;
  transition: width 0.3s ease;
}

.pages a:hover::after {
  width: 70%;
}

.search-container {
  position: relative;
  transition: all 0.3s ease;
}

.search-container.focused {
  transform: translateY(-2px);
}

.search-bar {
  min-width: 220px;
  font-size: 15px;
  padding: 10px 15px 10px 0;
  border: none;
  border-radius: 0 20px 20px 0;
  color: #444;
  caret-color: #43a047;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-bar:focus {
  outline: none;
  min-width: 240px;
}

.search-icon {
  color: #777;
  width: 45px;
  padding: 12px 0 12px 15px;
  background-color: white;
  border-radius: 20px 0 0 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-container.focused .search-icon {
  color: #43a047;
}

.landing {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.landing::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(67, 160, 71, 0.3), transparent 60%);
  z-index: 1;
}

.landing .container {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  align-items: center;
}

.landing-content {
  width: 100%;
  align-items: center;
}

.landing-text {
  color: white;
  width: 50%;
  max-width: 620px;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.landing-text.loaded {
  opacity: 1;
  transform: translateY(0);
}

.landing-text h1 {
  margin: 0 0 20px 0;
  font-size: 42px;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.5px;
}

.landing-text p {
  margin-bottom: 0;
  line-height: 1.8;
  font-size: 16px;
  font-weight: 300;
  opacity: 0.9;
}

.cta-container {
  display: flex;
  margin-top: 30px;
}

@media (max-width: 992px) {
  .landing-content {
    flex-direction: column;
    gap: 40px;
  }

  .landing-text {
    width: 100%;
    text-align: center;
    margin-top: 80px;
  }

  .landing-text h1 {
    font-size: 32px;
  }

  .cta-container {
    justify-content: center;
  }

  .img-holder {
    margin-bottom: 40px;
  }
}

.img-holder {
  position: relative;
  opacity: 0;
  transform: translateX(20px);
  transition: opacity 0.8s ease 0.3s, transform 0.8s ease 0.3s;
}

.img-holder.loaded {
  opacity: 1;
  transform: translateX(0);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
  100% { transform: translateY(0px); }
}

.img-holder::before {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  top: -20px;
  right: -15px;
  border-right: 3px solid rgba(255, 255, 255, 0.7);
  border-top: 3px solid rgba(255, 255, 255, 0.7);
  border-radius: 10px;
  z-index: 1;
}

.img-details {
  position: absolute;
  top: 30px;
  z-index: 2;
  right: 80px;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  gap: 15px;
  animation: fadeIn 1s ease-in-out;
}

.img-details .circle {
  width: 67px;
  height: 67px;
}

.img-details .circle img {
  width: 100%;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.img-details p {
  margin: 0;
  color: white;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.img-details p span {
  display: block;
}

.img {
  width: 450px;
  height: 280px;
  margin-right: 20px;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.img:hover {
  transform: scale(1.02);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.img::after {
  content: '';
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(67, 160, 71, 0.8), rgba(46, 125, 50, 0.9));
  position: absolute;
  top: 0;
  left: 90px;
  border-bottom-left-radius: 73%;
  transform: scale(1, 2) rotate(20deg);
  transition: all 0.5s ease;
}

@keyframes up-down {
  0% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0); }
}

.up-down {
  animation: up-down 1.5s ease-in-out infinite;
  transform: translateY(0);
}

.scroll-icon {
  position: absolute;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.scroll-icon:hover {
  transform: translate(-50%, -5px);
}

.mouse-icon {
  opacity: 0.8;
  transition: all 0.3s ease;
}

.scroll-icon:hover .mouse-icon {
  opacity: 1;
}

.arrow-icon {
  margin-top: -5px;
}

.card-read-more-btn {
  border-radius: 30px;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  cursor: pointer;
  color: white;
  background-color: #43a047;
  font-weight: 600;
  font-size: 13px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(67, 160, 71, 0.2);
}

.card-read-more-btn:hover {
  background-color: #2e7d32;
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(46, 125, 50, 0.3);
}

.read-more-btn {
  border-radius: 30px;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 10px 24px;
  margin-top: 30px;
  cursor: pointer;
  color: white;
  background-color: #43a047;
  font-weight: 500;
  font-size: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(67, 160, 71, 0.2);
}

.read-more-btn:hover {
  background-color: #2e7d32;
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(46, 125, 50, 0.3);
}

footer {
  /* background-color: rgb(9, 55, 24); */
  padding-top: 25px;
  padding-bottom: 10px;
  /* border-top-right-radius: 48px;
  border-top-left-radius: 48px; */
}

.social-icons {
  display: flex;
  justify-content: space-between;
  color: white;
  align-items: center;
}
.foot-logos {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 15px;
  gap: 0px;
}

.social-icons i {
  font-size: 30px;
  color: #A7ADB7;
  cursor: pointer;
  transition: .3s;
}
.social-icons i:hover {
  color: white;
}
.soials {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 370px;
  margin-left: 15px;
}

.phones {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 0px;
  text-align: center;
}
.phones span {
  color: white;
  margin-top: 10px;
}

.back-image {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
}

.video-background {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(39, 174, 96, 0.7), rgba(46, 204, 113, 0.4));
}

.back-image::after {
  content: '';
  background-color: rgb(0 0 0 / .8);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.join-articles {
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 50px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  backdrop-filter: blur(5px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.join-articles h2 {
  /* color: #0C490C; */
  color: white;
  position: relative;
  margin: 0;
  font-size: 40px;
}

.join-articles p {
  color: white;
  position: relative;
  margin-bottom: 0;
  font-size: 20px;
}

.articles {
  padding-top: 100px;
  padding-bottom: 100px;
  background-color: white;
}
.articles > .text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 700px;
  text-align: center;
  margin-bottom: 40px;
}

.articles .text h2 {
  margin: 0;
  color: #2e7d32;
  font-weight: 700;
  font-size: 36px;
}

.articles .text p {
  margin: 10px 0 0;
  color: #388e3c;
  font-size: 18px;
}

.articles .cards {
  padding: 50px 30px;
  border-radius: 10px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  box-shadow: rgba(0, 0, 0, 0.13) 0px 12px 40px 0px, rgba(0, 0, 0, 0.12) 0px 2px 4px 0px;
}

.articles .card {
  border-radius: 5px;
  box-shadow: 0 2px 15px rgb(0 0 0 / 10%);
  transition: .3s;
}
.articles .card:hover {
  transform: translateY(-10px);
  box-shadow: 0 2px 15px rgb(0 0 0 / 30%);
}
.articles .card img {
  max-width: 100%;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
.articles .card .text {
  padding: 20px;
}
.articles .card .text h3 {
  margin: 0;
}
.articles .card .text p {
  line-height: 1.6;
  color: #777;
  margin: 10px 0 0;
}
.articles .card .more {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-top: 1px solid #e6e6e7;
}
.articles .card .more a {
  font-size: 14px;
  font-weight: bold;
  color: var(--main-color);
  display: block;
}
.articles .card .more i {
  color: var(--main-color);
  font-size: 14px;
}
.articles .card:hover i {
  animation: bouncing-left .5s infinite linear;
}

.articles-container {
  margin-top: 30px;
  margin-bottom: 50px;
  border: 1px solid #333;
  border-radius: 12px;
  padding: 50px 50px 0px 50px;
  background-color: #E6DFDC80;
}

.articles-page {
  padding-top: 130px;
  /* font-family: "El Messiri", serif; */
}

.head-line {
  border: 1px solid #43a047;
  padding: 10px 0 6px;
  border-radius: 20px;
  background-color: white;
  text-align: center;
  font-weight: bold;
  font-size: 20px;
  margin-top: 30px;
}

.articles-types {
  padding: 10px;
  margin: 40px 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 50px;
}

.article-card {
  background-image: url('./components/imgs/ArticleBackground1.jpg');
  background-size: cover;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  width: calc((100% - 100px) / 3);
  height: 240px;
  padding: 15px;
  cursor: pointer;
}
.article-card::before {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(67, 160, 71, 0.22);
  transition: .3s;
}

.article-card .article-img {
  filter: grayscale(0%);
  transition: .3s;
}

.article-card:hover::before {
  background-color: rgb(0 0 0 / .8);
}

.article-card:hover .article-img {
  filter: grayscale(100%);
}

.img-wrapper {
  width: 120px;
  height: 120px;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-20%, -65%);
  border: 8px solid rgba(67, 160, 71, 0.7);
  outline: 8px solid rgba(67, 160, 71, 0.5);
}
.article-img {
  min-width: 100%;
  max-width: 100%;
  height: 100%;
}

.dis-main-info {
  background-color: white;
  border-radius: 15px;
  align-items:normal;
  position: relative;
  margin-bottom: 40px;
}

.dis-main-text {
  min-height: 100%;
  padding: 20px 20px 20px 40px;
  width: 400px;

}
.dis-main-text h1 {
  margin: 0;
  font-size: 20px;
}

.dis-main-text p {
  color: #181920B2;
  margin-top: 10px;
}

.dis-main-text > div {
  position: absolute;
  bottom: 30px;
  font-size: 18px;
  font-weight: 600;
}


.dis-main-info .dis-img {
  width: 400px;
  height: 270px;
  overflow: hidden;
  border-radius: 20px;
}

.dis-main-info .dis-img img {
  width: 100%;
  height: 100%;
}

.dis-details {
  background-color: white;
  border-radius: 15px;
  /* align-items:normal; */
  position: relative;
  padding: 30px;
}

.dis-details h1 {
  margin: 0;
  font-size: 22px;
}
.dis-details p {
  margin: 0;
  font-size: 17px;
  color: #333;
  padding-left: 10px;
}

.dis-details ul {
  padding: 0 0 0 10px;
  margin: 0;
  color: #333;
  font-size: 17px;
  list-style-position: inside;
}

.dis-details ul.correct {
  list-style: none;
  padding: 0 0 0 5px;
}
.dis-details ul.correct li::before {
  content: "\f00c";
  font-family: "Font Awesome 5 Free";
  margin-right: 10px;
  color: green;
  font-weight: 900;
}
.tip {
  list-style: none;
  padding: 0 0 0 6px;
}
.tip::before {
  content: '\f08d';
  font-family: "Font Awesome 5 Free";
  margin-right: 10px;
  color: red;
  font-weight: 900;
}

.art-states {
  /* border-top: 1px solid #aaa;
  border-bottom: 1px solid #aaa; */
  /* border-top: 1px solid #71767B;
  border-bottom: 1px solid #71767B; */
  margin-top: 30px;
  /* margin-bottom: 20px; */
  background-color: white;
  border-radius: 5px;
}
.art-states div {
  padding-top: 10px;
  padding-bottom: 10px;
  flex-basis: 50%;
  color: #71767B;
  font-size: 18px;
  text-align: center;
  transition: .3s;
  cursor: pointer;
  border-radius: 5px;
}
.art-states div:hover {
  background-color: #B0BEC5;
  color: white;
}

.comments-section {
  margin-top: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
}
.comments-section  .comment {
  display: flex;
  /* margin-bottom: 20px; */
  margin-bottom: 15px;
}
/* .comments-section .comment:not(:last-child) {
} */
.comments-section  .comment .user-img {
  min-width: 30px;
  max-width: 30px;
  margin-right: 15px;
  margin-top: 1px;
}
.comments-section  .comment .user-img img {
  width: 100%;
}
.comments-section .comment .content {
  /* flex: 1; */
  background-color: hsl(0deg 0% 100% / 50%);
  padding: 5px 12px;
  border-radius: 20px;
}

.comment .content .user-name {
  width: fit-content;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 2px;
}

.comment .content .comment-text {
  font-size: 15px;
  word-break: break-word;
}

.write-comment {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}
 .write-comment img {
  max-width: 32px;
  height: 32px;
  margin-right: 15px;
}
.write-comment > div {
  display: flex;
  justify-content: space-between;
  /* align-items: center; */
  flex: 1;
}

.write-comment input {
  display: block;
  width: 100%;
  margin: 0 auto;
  border: 1px solid #7F7F7F;
  padding: 8px 15px;
  border-radius: 14px 0 0 14px;
  font-size: 17px;
  color: #333;
  caret-color: #444;
  background-color: #dedede;
  border-right: none;
}

.write-comment input:focus {
  outline: none;
}

.write-comment > div > div {
  width: 40px;
  border-radius: 0 14px 14px 0;
  border: 1px solid #7F7F7F;
  border-left: none;
  background-color: #dedede;
  cursor: pointer;
}
.write-comment > div svg {
  width: 25px;
}

.filterBtn {
  background-color: white;
  border-radius: 10px;
  padding: 5px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}

.sub-cat-head {
  border: 1px solid #A7ADB7;
  background-color: white;
  border-radius: 15px;
  display: flex;
  justify-content: space-between;
  /* align-items: center; */
}

.sub-cat-head ul {
  padding: 0;
  margin: 0;
  list-style-position: inside;
}

.sub-cat-head-img {
  background-image: url('./components/imgs/ArticleBackground.jpg');
  width: 500px;
  min-height: 100%;
  padding: 20px;
  color: white;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 10px;
  border-radius: 15px;
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;

}

.sub-cat-head-img img {
    width: 145px;
    height: 150px;
    border-radius: 50%;
    position: absolute;
    left: 130px;
    top: -30px;
    border: 7px solid #43a047;
}

.sub-cat-search {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #A7ADB7;
  border-radius: 15px;
  margin: 50px 0;
  padding: 5px 20px;
}

.sub-cat-search h2 {
  border-bottom: 3px solid #43a047;
}

.sub-cat-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  align-content: center;
}

.sub-cat-card {
  /* border: 1px solid #021930; */
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 4px #00000040;
  background-color: white;
}

.sub-cat-card .cat-head {
  background-image: url('./components/imgs/ArticleBackground.jpg');
  background-size: cover;
  height: 160px;
  position: relative;
}
.sub-cat-card  img {
  width: 110px;
  height: 110px;
  border-radius: 50%;
  border: 5px solid #43a047;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-115%, -55%);
}

.sub-cat-card .cat-text {
  padding: 20px 20px 30px 20px;
}
.sub-cat-card .cat-text h3 {
  margin: 0;
  color: #2e7d32;
  font-size: 20px
}
.sub-cat-card .cat-text p {
  font-size: 15px;
  line-height: 2;
  color: #333;
  margin-bottom: 0;
  margin-top: 5px;
}

.sub-cat-card  .cat-foot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
}

.sub-cat-card  .cat-foot span {
  font-size: 14px;
    margin-bottom: -3px;
    color: #173759;
    font-weight: bold;
}

.sub-cat-card  .cat-foot span i {
  color: #072A4E87;
}

.cat-art-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  align-content: center;
}

.cat-art-card {
  background-color: white;
  border-radius: 15px;
}

.cat-art-card-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  padding: 35px 25px;
  border-radius: 15px 15px 0 0;
}
.cat-art-card-head h3 {
  margin: 0;
  font-size: 22px;
}
.cat-art-card-head img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
}


.cat-art-card-text {
  padding: 20px;
  color: #333;
  line-height: 1.8;
  padding-bottom: 0;
  font-size: 15px;
}


.cat-art-card-foot{
  display: flex;
  flex-direction: row-reverse;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
}


.cat-art-card-foot span {
  font-size: 14px;
    margin-bottom: -3px;
    color: #173759;
    font-weight: bold;
}

.cat-art-card-foot span i {
  color: #072A4E87;
}

.sign-btn {
  background-color: white;
  padding: 10px 15px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: bold;
  color: #2e7d32;
  border: none;
  cursor: pointer;
}

.login-page,
.register-page {
  padding: 20px;
  height: 100vh;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  transition: .3s;
}

.login-logo,
.register-logo {
  position: absolute;
  width: 50px;
}
.login-logo {
  left: 0px;
  top: 20px;
}

.register-logo {
  left: 100px;
  top: 40px;
}


.login-form,
.register-form {
  font-family: "Poppins", sans-serif;
}

.login-form > h1,
.register-form > h1 {
  margin: 0;
  font-weight: normal;
  font-size: 40px;
}

.login-form > h1 + p,
.register-form > h1 + p {
  color: #939393;
  margin-top: -5px;
  margin-bottom: 50px;
  font-weight: normal;
  font-size: 18px;
}

.login-form input,
.register-form input {
  display: block;
  width: calc(100% - 40px);
  padding: 18px 20px;
  margin-right: auto;
  border-radius: 25px 0 0 25px;
  border: 1px solid #939393;
  border-right: none;
  outline: none;
  color: #333;
  letter-spacing: 1px;
  font-size: 17px;
  position: relative;
  max-width: 350px;
}

.login-form input::placeholder,
.register-form input::placeholder {
  font-size: 14px;
  color: #939393;
}

.login-form input + div,
.register-form input + div {
  padding: 16px 20px;
  margin-right: auto;
  border-radius: 0 25px 25px 0;
  border: 1px solid #939393;
  border-left: none;
  color: #939393;
}
.login-form input + div i,
.register-form input + div i {
  width: 20px;
}
.login-form .reg-btn,
.login-form .login-btn {
  margin-top: 30px;
  padding: 10px 20px;
  color: white;
  background-color: #22766e;
  border: none;
  outline: none;
  font-size: 17px;
  border-radius: 7px;
  cursor: pointer;
}

.login-form .options-head,
.register-form .options-head {
  position: relative;
  color: #939393;
  margin: 30px auto;
  font-size: 14px;
  padding: 0 10px;
  background-color: white;
  width: fit-content;
}

.login-form .options-head::before,
.register-form .options-head::before,
.login-form .options-head::after,
.register-form .options-head::after {
  content: '';
  width: 100px;
  height: 1px;
  background-color: #939393;
  position: absolute;
  top: 50%;
}

.options-head::before {
  left: calc(0px - 100px);
}
.options-head::after {
  right: calc(0px - 100px);
}

.sign-with-acc {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  margin-bottom: 32px;
}
.sign-with-acc svg {
  width: 40px;
  cursor: pointer;
}

.svg-container {
  width: 60px;
  height: 60px;
  border: 1px solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.profile {
  padding-top: 150px;
  padding-bottom: 50px;
  display: flex;
  justify-content: space-between;
}


.profile .profile-content {
  flex: 1;
}

.profile .profile-content h2 {
  margin: 0;
  margin-bottom: 10px;
  font-size: 40px;
}

.profile .profile-content .profile-nav {
  width: 80%;
  border-bottom: 1px solid #eee;
  margin-bottom: 30px;
}
.lol {
  text-decoration: underline;
  transition: all .3s;
}
.lol:hover {
  text-decoration: none;
}
.profile .profile-content .profile-nav span {
  display: inline-block;
  padding: 10px 10px 15px 10px;
  font-size: 17px;
  color: #939393;
  cursor: pointer;
}

.posts-container {
  width: 80%;
  border: 1px solid #ddd;
  border-radius: 6px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  gap: 10px;
  cursor: pointer;
}

.posts-container .posts-info {
  flex-basis: 50%;
  padding: 15px;
}

.posts-container .posts-info .user-data {
  display: flex;
  align-items: center;
  gap: 10px;

}
.posts-container .posts-info .user-data img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.posts-container .posts-info .user-data div {
  font-weight: 500;
  font-size: 17px;
}


.posts-container .posts-info .list-name {
  font-weight: 700;
  font-size: 25px;
  margin: 20px 0px 20px 5px;
}

.posts-container .posts-info .options {
  margin-left: 5px;
  display: flex;
  color: #888;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.posts-container .posts-imgs {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
  max-height: 200px;
}

.posts-container .posts-imgs .img-container {
  height: 100%;
  background-color: #D9D9D9;
  max-height: 100%;
}

.posts-container .posts-imgs .img-container:nth-child(1) {
  width: 50%;
}
.posts-container .posts-imgs .img-container:nth-child(2) {
  width: 30%;
}
.posts-container .posts-imgs .img-container:nth-child(3) {
  width: 20%;
}


.profile .user-info {
  padding-top: 20px;
  flex-basis: 30%;
  border-left: 1px solid #eee;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.profile .user-info img {
  width: 200px;
  border-radius: 50%;
}


@media (min-width: 768px) {
  .create-post-page > .container  {
    width: 700px;
  }
}
/* Medium */
@media (min-width: 992px) {
  .create-post-page > .container  {
    width: 900px;
  }
}
/* Large */
@media (min-width: 1200px) {
  .create-post-page > .container  {
    width: 1000px;
  }
}

.main-title-input {
  display: block;
  /* width: calc(100% - 200px); */
  width: 100%;
  height: 100%;
  padding: 10px 20px;
  margin: 0 auto 40px;
  font-size: 40px;
  font-weight: 100;
  border: none;
  border-left: 1px solid #d3d1d1;
  border-right: 1px solid #d3d1d1;
  text-align: center;
  caret-color: #555;
}
.main-title-input::placeholder {
  color: #d3d1d1;
  font-weight: 100;
}

.main-title-input:focus {
  outline: none;
}

.upload-file-box {
  width: 450px;
  border-radius: 10px;
  margin: 50px auto;
  padding: 10px 20px;
  background-color: #ECEFF1;
  color: #607D8B;
}
/* Hide the real file input */
input[type="file"] {
  display: none;
}

/* Style the custom label */
.custom-file-upload {
  display: inline-block;
  padding: 10px 20px;
  background-color: #78909C;
  color: white;
  cursor: pointer;
  border-radius: 6px;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.custom-file-upload:hover {
  background-color: #546E7A;
}

.post-description {
  margin-bottom: 40px;
  flex: 1;
}
.post-description .title {
  height: 70px;
  margin-bottom: 20px;
}

.post-description .title input,
.post-description .description textarea  {
  width: 100%;
  height: 100%;
  padding: 10px 20px;
  font-size: 40px;
  font-weight: 100;
  border: none;
  border-left: 1px solid #d3d1d1;
  caret-color: #555;
}

.post-description .title input::placeholder,
.post-description .description textarea::placeholder  {
  color: #d3d1d1;
  font-weight: 100;
}

.post-description .title input:focus,
.post-description .description textarea:focus  {
  outline: none;

}

.post-description .description textarea {
  height: 100px;
  max-height: 150px;
  font-size: 20px;
  resize: none;
  color: #444;
}

.post-description .description textarea::placeholder {
  font-size: 30px;
}

.post-controls svg {
  color: #d3d1d1;
  cursor: pointer;
  width: 50px;
  height: 50px;
  vertical-align: middle;
}

.drop-menu {
  width: 100px;
  position: absolute;
  top: calc(100% - 5px);
  left: 40px;
  background-color: white;
  border-radius: 7px;
  list-style: none;
  padding: 0;
  margin: 0;
}
.drop-menu li a {
  color: #555;
  padding: 7px 13px;
  display: block;
  font-size: 14px;
  font-weight: 500;
  transition: .3s;
}
.drop-menu li:not(:last-child) a {
  border-bottom: 1px solid #d3d1d1;
}
.drop-menu li a:hover {
  padding-left: 18px;
}






@keyframes bouncing-left {
  100% {
      transform: translateX(10px);
  }
}

@keyframes up-down {
  0% {
    transform: translateY(10px);
  }
  50% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(10px);
  }
}
/* ======================================================================================== */
.contact {
  padding-top: 130px;
  padding-bottom: 100px;
}
.contact-main-head {
  /* font-family: "El Messiri", serif; */
  font-weight: 700;
  font-size: 35px;
  margin: 0;
  padding: 0 10px;
  color: white;
  border-bottom: 3px solid #FFC300;
  width: fit-content;
  border-radius: 3px;
  margin-bottom: 40px;
}

.contact form {
  width: 600px;
}
.contact form .row {
  margin-bottom: 15px;
}
.contact form label {
  color: white;
  text-transform: capitalize;
  display: block;
  margin-bottom: 10px;
  margin-left: 10px;
  font-size: 14px;
}
.contact form input {
  padding: 15px;
  border: none;
  outline: none;
  border-radius: 50px;
  width: 280px;
}
.contact form textarea {
  width: 100%;
  resize: none;
  min-height: 300px;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  outline: none;
  border: none;
}

.contact form button[type='submit'] {
  width: 100%;
  padding: 14px;
  border-radius: 10px;
  outline: none;
  border: none;
  background-color: #021930;
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
}

.links {
  padding: 100px 0;
}
.social-cards {
  width: 60%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 30px;
}
.social-cards .card {
  background-color: #edf2fd;
  padding: 16px 40px;
  border-radius: 8px;
  font-size: 15px 20px;
  font-weight: 500;
  color: #222;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: .5s;
  width: 300px;
}

.social-cards .card:hover {
  background-color: #1b5e20;
  color: white;
}

.social-cards .card i {
  font-size: 30px;
  margin-right: 10px;
}













































































































































/*



.video {
  position: relative;
}
.video::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgb(0 0 0 / 80%);
}
.video video {
  width: 100%;
}

.video .text {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  background-color: var(--transparent-color);
  text-align: center;
  padding: 50px;
  color: white;
}
.video .text h2 {
  font-weight: normal;
  text-transform: uppercase;
}
.video .text p {
  margin: 30px  0;
}
.video .text button {
  border: none;
  padding: 10px 20px;
  text-transform: uppercase;
  background-color: black;
  color: white;
}
 */

